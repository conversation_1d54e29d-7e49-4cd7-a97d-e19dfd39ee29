import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { ArrowLeft, Plus, Minus, ShoppingCart, Check } from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';

type Addition = {
  id: string;
  name: string;
  price: number;
};

const SupplierProductDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const productId = searchParams.get('productId');
  const supplierId = searchParams.get('supplierId');
  const category = searchParams.get('category');

  // Get product and supplier data from location state or use defaults
  const product = location.state?.product || {
    id: productId,
    name: 'Product Name',
    price: 25,
    image: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=400&fit=crop',
    description: 'Delicious product with fresh ingredients',
    category: 'main',
    restaurantOptions: {
      additions: [
        { id: 'extra-sauce', name: 'Extra Sauce', price: 2 },
        { id: 'extra-cheese', name: 'Extra Cheese', price: 3 },
        { id: 'extra-vegetables', name: 'Extra Vegetables', price: 2 }
      ],
      without: ['Onions', 'Tomatoes', 'Pickles'],
      sides: [
        { id: 'fries', name: 'French Fries', price: 8 },
        { id: 'salad', name: 'Side Salad', price: 6 }
      ]
    },
    clothingOptions: {
      sizes: ['XS', 'S', 'M', 'L', 'XL'],
      colors: ['Red', 'Blue', 'Green', 'Black', 'White'],
      gallery: []
    }
  };

  const supplier = location.state?.supplier || {
    id: supplierId,
    name: 'Supplier Name'
  };

  const { addItem } = useCartStore();

  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState(product.clothingOptions?.sizes?.[0] || '');
  const [selectedColor, setSelectedColor] = useState(product.clothingOptions?.colors?.[0] || '');
  const [selectedAdditions, setSelectedAdditions] = useState<string[]>([]);
  const [selectedSides, setSelectedSides] = useState<string[]>([]);
  const [without, setWithout] = useState<string[]>([]);
  const [showSuccess, setShowSuccess] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Scroll effect for hero image
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const increaseQuantity = () => setQuantity(prev => prev + 1);
  const decreaseQuantity = () => setQuantity(prev => Math.max(1, prev - 1));

  const toggleAddition = (additionId: string) => {
    setSelectedAdditions(prev =>
      prev.includes(additionId)
        ? prev.filter(id => id !== additionId)
        : [...prev, additionId]
    );
  };

  const toggleSide = (sideId: string) => {
    setSelectedSides(prev =>
      prev.includes(sideId)
        ? prev.filter(id => id !== sideId)
        : [...prev, sideId]
    );
  };

  const toggleWithout = (item: string) => {
    setWithout(prev =>
      prev.includes(item)
        ? prev.filter(w => w !== item)
        : [...prev, item]
    );
  };

  const calculateTotalPrice = () => {
    const basePrice = product.price;
    const additionsPrice = selectedAdditions.reduce((sum, additionId) => {
      const addition = product.restaurantOptions?.additions?.find(a => a.id === additionId);
      return sum + (addition?.price || 0);
    }, 0);
    const sidesPrice = selectedSides.reduce((sum, sideId) => {
      const side = product.restaurantOptions?.sides?.find(s => s.id === sideId);
      return sum + (side?.price || 0);
    }, 0);

    return (basePrice + additionsPrice + sidesPrice) * quantity;
  };

  const handleAddToCart = () => {
    const finalPrice = calculateTotalPrice() / quantity; // price per item including options

    if (category === 'restaurants') {
      addItem({
        product,
        qty: quantity,
        finalPrice,
        supplierId: supplier.id,
        supplierName: supplier.name,
        supplierCategory: 'restaurants',
        selectedAdditions: product.restaurantOptions?.additions?.filter(a => selectedAdditions.includes(a.id)) || [],
        selectedSides: product.restaurantOptions?.sides?.filter(s => selectedSides.includes(s.id)) || [],
        without,
      });
    } else {
      addItem({
        product,
        qty: quantity,
        finalPrice,
        supplierId: supplier.id,
        supplierName: supplier.name,
        supplierCategory: 'clothings',
        selectedSize,
        selectedColor,
      });
    }

    setShowSuccess(true);
    setTimeout(() => setShowSuccess(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Success Notification */}
      {showSuccess && (
        <div className="fixed bottom-24 left-1/2 transform -translate-x-1/2 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg animate-bounce">
          <div className="flex items-center gap-2">
            <Check className="w-5 h-5" />
            <span className="font-semibold">Added to cart ✅</span>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-white/90 backdrop-blur-sm shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="text-xl font-bold text-gray-900">{product.name}</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="pt-20 pb-32">
        {/* Hero Image Section */}
        <div className="relative h-64 overflow-hidden">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover"
            style={{
              transform: `scale(${1 + scrollY * 0.0005})`,
              opacity: Math.max(0.4, 1 - scrollY * 0.002)
            }}
          />
          <div className="absolute inset-0 bg-black/45"></div>
          <div className="absolute bottom-4 left-4 text-white">
            <h2 className="text-3xl font-bold mb-1">{product.name}</h2>
            <p className="text-gray-200 text-lg">₪{product.price.toFixed(2)}</p>
          </div>
        </div>

        {/* Content Sections */}
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 space-y-6 mt-6">

          {/* Restaurant Options */}
          {category === 'restaurants' && (
            <>
              {/* Additions */}
              {product.restaurantOptions?.additions && product.restaurantOptions.additions.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Additions</h3>
                  <div className="space-y-3">
                    {product.restaurantOptions.additions.map((addition: Addition) => (
                      <label
                        key={addition.id}
                        className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedAdditions.includes(addition.id)}
                            onChange={() => toggleAddition(addition.id)}
                            className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                          />
                          <span className="font-medium text-gray-900">{addition.name}</span>
                        </div>
                        {addition.price > 0 && (
                          <span className="text-purple-600 font-semibold">+₪{addition.price}</span>
                        )}
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Without Options */}
              {product.restaurantOptions?.without && product.restaurantOptions.without.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Without</h3>
                  <div className="space-y-3">
                    {product.restaurantOptions.without.map((item: string) => (
                      <label
                        key={item}
                        className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <input
                          type="checkbox"
                          checked={without.includes(item)}
                          onChange={() => toggleWithout(item)}
                          className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                        />
                        <span className="font-medium text-gray-900">Without {item}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Sides */}
              {product.restaurantOptions?.sides && product.restaurantOptions.sides.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Side Orders</h3>
                  <div className="space-y-3">
                    {product.restaurantOptions.sides.map((side: Addition) => (
                      <label
                        key={side.id}
                        className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedSides.includes(side.id)}
                            onChange={() => toggleSide(side.id)}
                            className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                          />
                          <span className="font-medium text-gray-900">{side.name}</span>
                        </div>
                        <span className="text-purple-600 font-semibold">+₪{side.price}</span>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Clothing Options */}
          {category === 'clothings' && (
            <>
              {/* Image Gallery */}
              {product.clothingOptions?.gallery && product.clothingOptions.gallery.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <div className="grid grid-cols-2 gap-4">
                    {product.clothingOptions.gallery.map((image: string, index: number) => (
                      <img
                        key={index}
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Size Selection */}
              {product.clothingOptions?.sizes && product.clothingOptions.sizes.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Size</h3>
                  <div className="flex flex-wrap gap-3">
                    {product.clothingOptions.sizes.map((size: string) => (
                      <button
                        key={size}
                        onClick={() => setSelectedSize(size)}
                        className={`px-4 py-2 border-2 rounded-lg font-medium transition-colors ${
                          selectedSize === size
                            ? 'border-purple-500 bg-purple-500 text-white'
                            : 'border-gray-300 text-gray-700 hover:border-gray-400'
                        }`}
                      >
                        {size}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Color Selection */}
              {product.clothingOptions?.colors && product.clothingOptions.colors.length > 0 && (
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Color</h3>
                  <div className="flex flex-wrap gap-3">
                    {product.clothingOptions.colors.map((color: string) => (
                      <button
                        key={color}
                        onClick={() => setSelectedColor(color)}
                        className={`px-4 py-2 border-2 rounded-lg font-medium transition-colors ${
                          selectedColor === color
                            ? 'border-purple-500 bg-purple-500 text-white'
                            : 'border-gray-300 text-gray-700 hover:border-gray-400'
                        }`}
                      >
                        {color}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Quantity Section */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quantity</h3>
            <div className="flex items-center gap-4">
              <button
                onClick={decreaseQuantity}
                className="w-12 h-12 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <Minus className="w-5 h-5" />
              </button>
              <span className="text-xl font-semibold text-gray-900 min-w-[3rem] text-center">{quantity}</span>
              <button
                onClick={increaseQuantity}
                className="w-12 h-12 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <Plus className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Bottom Button */}
      <div className="fixed bottom-0 left-0 right-0 z-30 bg-white border-t border-gray-200 p-4">
        <div className="max-w-4xl mx-auto">
          <button
            onClick={handleAddToCart}
            className="w-full bg-purple-600 text-white py-4 rounded-lg font-semibold text-lg hover:bg-purple-700 transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <ShoppingCart className="w-5 h-5" />
            Add {quantity} • ₪{calculateTotalPrice().toFixed(2)}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SupplierProductDetailsPage;
