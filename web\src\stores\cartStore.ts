import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Addition = { id: string; name: string; price: number };

export type ProductType = {
  id: string;
  name: string;
  image: string;
  price: number;
  category: string;
  restaurantOptions?: {
    additions?: Addition[];
    without?: string[];
    sides?: Addition[];
  };
  clothingOptions?: {
    sizes: string[];
    colors: string[];
    gallery: string[];
  };
};

export type CartItem = {
  id: number;
  product: ProductType;
  qty: number;
  finalPrice: number;
  supplierId: string;
  supplierName: string;
  supplierCategory: string;
  // for restaurant item
  selectedAdditions?: Addition[];
  selectedSides?: Addition[];
  without?: string[];
  // for clothing item
  selectedSize?: string;
  selectedColor?: string;
};

type CartState = {
  items: CartItem[];
  // Accept full CartItem minus id (which we'll assign)  
  addItem: (payload: Omit<CartItem, 'id'>) => void;
  removeItem: (id: number) => void;
  updateQty: (id: number, qty: number) => void;
  clearCart: () => void;
  totalQty: (supplierId: string) => number;
  totalPrice: (supplierId: string) => number;
  getItemsBySupplier: () => Record<string, CartItem[]>;
  getTotalItems: () => number;
  getTotalPrice: () => number;
};

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],

      addItem: (payload) => {
        const items = get().items;

        // compare by product id + options
        let existing;
        if (payload.supplierCategory === "restaurants") {
          existing = items.find(i => 
            i.product.id === payload.product.id &&
            i.supplierId === payload.supplierId &&
            JSON.stringify(i.selectedAdditions) === JSON.stringify(payload.selectedAdditions) &&
            JSON.stringify(i.selectedSides) === JSON.stringify(payload.selectedSides) &&
            JSON.stringify(i.without) === JSON.stringify(payload.without)
          );
        } else if (payload.supplierCategory === "clothings") {
          existing = items.find(i => 
            i.product.id === payload.product.id &&
            i.supplierId === payload.supplierId &&
            i.selectedSize === payload.selectedSize &&
            i.selectedColor === payload.selectedColor
          );
        } else {
          existing = items.find(i => 
            i.product.id === payload.product.id &&
            i.supplierId === payload.supplierId
          );
        }
        
        if (existing) {
          set({
            items: items.map(i => 
              i === existing
                ? { ...i, qty: i.qty + payload.qty }       // stack quantities
                : i
            )
          });
        } else {
          set({
            items: [
              ...items,
              { ...payload, id: Date.now() }               // new row in cart
            ]
          });
        }
      },

      removeItem: (id) => {
        set({
          items: get().items.filter(i => i.id !== id)
        });
      },

      updateQty: (id, qty) => {
        if (qty < 1) return;
        set({
          items: get().items.map(i =>
            i.id === id ? { ...i, qty } : i
          )
        });
      },

      clearCart: () => set({ items: [] }),

      totalQty: (supplierId) => {
        return get()
          .items.filter(i => i.supplierId === supplierId)
          .reduce((sum, i) => sum + i.qty, 0);
      },

      totalPrice: (supplierId) => {
        return get()
          .items.filter(i => i.supplierId === supplierId)
          .reduce((sum, i) => sum + i.finalPrice * i.qty, 0);
      },

      getItemsBySupplier: () => {
        return get().items.reduce((acc, item) => {
          if (!acc[item.supplierId]) acc[item.supplierId] = [];
          acc[item.supplierId].push(item);
          return acc;
        }, {} as Record<string, CartItem[]>);
      },

      getTotalItems: () => {
        return get().items.reduce((sum, item) => sum + item.qty, 0);
      },

      getTotalPrice: () => {
        return get().items.reduce((sum, item) => sum + (item.finalPrice * item.qty), 0);
      }
    }),
    {
      name: 'wasel-cart-storage', // unique name for localStorage
    }
  )
);
