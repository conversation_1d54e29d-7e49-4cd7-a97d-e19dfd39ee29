module.exports = function (api) {
  api.cache(true);
  let plugins = [];

  // Enable Tamagui babel plugin for all environments
  plugins.push([
    '@tamagui/babel-plugin',
    {
      components: ['tamagui'],
      config: './tamagui.config.ts',
      logTimings: true,
    }
  ]);

  plugins.push('react-native-reanimated/plugin');

  return {
    presets: ['babel-preset-expo'],
    plugins,
  };
};
