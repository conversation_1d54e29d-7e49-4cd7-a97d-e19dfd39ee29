import mongoose, { Document, Schema } from 'mongoose';

export interface AIMessage extends Document {
  _id: string;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    tokens?: number;
    model?: string;
    processingTime?: number;
    confidence?: number;
  };
  feedback?: {
    rating?: number;
    helpful?: boolean;
    comment?: string;
  };
}

const MessageSchema = new Schema<AIMessage>({
  conversationId: {
    type: String,
    required: true,
    index: true
  },
  role: {
    type: String,
    required: true,
    enum: ['user', 'assistant', 'system'],
    index: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  metadata: {
    tokens: Number,
    model: String,
    processingTime: Number,
    confidence: Number
  },
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    helpful: <PERSON><PERSON><PERSON>,
    comment: String
  }
}, {
  timestamps: true,
  collection: 'ai_messages'
});

// Indexes for better query performance
MessageSchema.index({ conversationId: 1, timestamp: 1 });
MessageSchema.index({ conversationId: 1, role: 1 });
MessageSchema.index({ timestamp: -1 });

// Instance methods
MessageSchema.methods.addFeedback = function(rating: number, helpful?: boolean, comment?: string) {
  this.feedback = {
    rating,
    helpful,
    comment
  };
  return this.save();
};

MessageSchema.methods.updateMetadata = function(metadata: any) {
  this.metadata = { ...this.metadata, ...metadata };
  return this.save();
};

// Static methods
MessageSchema.statics.findByConversation = function(conversationId: string, limit?: number) {
  const query = this.find({ conversationId }).sort({ timestamp: 1 });
  return limit ? query.limit(limit) : query;
};

MessageSchema.statics.findUserMessages = function(conversationId: string) {
  return this.find({ conversationId, role: 'user' }).sort({ timestamp: 1 });
};

MessageSchema.statics.findAssistantMessages = function(conversationId: string) {
  return this.find({ conversationId, role: 'assistant' }).sort({ timestamp: 1 });
};

MessageSchema.statics.getConversationStats = function(conversationId: string) {
  return this.aggregate([
    { $match: { conversationId } },
    {
      $group: {
        _id: '$role',
        count: { $sum: 1 },
        avgTokens: { $avg: '$metadata.tokens' },
        avgProcessingTime: { $avg: '$metadata.processingTime' }
      }
    }
  ]);
};

export const MessageModel = mongoose.model<AIMessage>('Message', MessageSchema);
