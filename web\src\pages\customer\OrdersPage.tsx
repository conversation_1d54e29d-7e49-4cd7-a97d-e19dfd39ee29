import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Clock,
  CheckCircle,
  Truck,
  Eye,
  MapPin,
  Phone,
  CreditCard,
  Calendar,
  Filter,
  Search,
  X,
  Navigation
} from 'lucide-react';
import { useOrdersStore } from '../../stores/ordersStore';
import { getUserOrders } from '../../../../services/apiService';

type OrderStatus = 'All' | 'Delivered' | 'On the Way' | 'Preparing' | 'Pending' | 'Confirmed' | 'Ready' | 'Cancelled';

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { orders, addMultipleOrders } = useOrdersStore();
  const [selectedFilter, setSelectedFilter] = useState<OrderStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch orders from backend on component mount
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const { orders: backendOrders } = await getUserOrders();

        // Convert backend orders to store format and add them
        const formattedOrders = backendOrders.map((order: any) => ({
          id: order.orderId,
          items: order.items.map((item: any) => ({
            product: { name: item.productName },
            qty: item.quantity,
            finalPrice: item.price
          })),
          supplier: { name: order.supplierName },
          total: order.totalAmount,
          estimatedTime: order.estimatedDeliveryTime || '45-60 mins',
          address: order.deliveryAddress.street,
          phone: order.customerPhone || '',
          paymentMethod: order.paymentMethod,
          status: order.status,
          placedAt: order.createdAt
        }));

        addMultipleOrders(formattedOrders);
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [addMultipleOrders]);

  const filters: { key: OrderStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Orders', color: 'bg-gray-100 text-gray-700' },
    { key: 'Delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'On the Way', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'Preparing', label: 'Preparing', color: 'bg-blue-100 text-blue-700' },
    { key: 'Pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' }
  ];

  const filteredOrders = useMemo(() => {
    let filteredOrders = selectedFilter === 'All' ? orders : orders.filter(order => order.status === selectedFilter);

    if (searchQuery.trim()) {
      filteredOrders = filteredOrders.filter(order =>
        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.items.some(item => item.product.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filteredOrders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [orders, selectedFilter, searchQuery]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={16} className="text-green-600" />;
      case 'On the Way': return <Truck size={16} className="text-orange-600" />;
      case 'Preparing': return <Clock size={16} className="text-blue-600" />;
      default: return <Package size={16} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-600 bg-green-50 border-green-200';
      case 'On the Way': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'Preparing': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-100/20 to-third-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-secondary-100/20 to-primary-100/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
            <p className="text-gray-600">Track and manage your orders</p>
          </div>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-6"
        >
          <div className="bg-white rounded-xl shadow-md border border-gray-200">
            <div className="flex items-center gap-3 p-4">
              <Search size={20} className="text-gray-400" />
              <input
                type="text"
                placeholder="Search orders, restaurants, or items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-transparent text-gray-800 placeholder-gray-500 outline-none text-base"
              />
              {searchQuery.trim() && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X size={18} className="text-gray-400" />
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Filter Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-6"
        >
          <div className="bg-white rounded-xl shadow-md border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-3">
              <Filter size={18} className="text-gray-600" />
              <span className="font-medium text-gray-700">Filter Orders</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.map((filter) => (
                <button
                  key={filter.key}
                  onClick={() => setSelectedFilter(filter.key)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedFilter === filter.key
                      ? 'bg-primary-600 text-white shadow-lg scale-105'
                      : filter.color + ' hover:scale-105'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Orders List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-4"
        >
          {filteredOrders.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl p-12 text-center shadow-lg border border-gray-100"
            >
              <Package size={64} className="text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No Orders Found</h3>
              <p className="text-gray-500">
                {searchQuery.trim()
                  ? `No orders match "${searchQuery}"`
                  : selectedFilter === 'All'
                    ? "You haven't placed any orders yet"
                    : `No ${selectedFilter.toLowerCase()} orders found`
                }
              </p>
            </motion.div>
          ) : (
            <AnimatePresence>
              {filteredOrders.map((order, index) => (
                <motion.div
                  key={order.id}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -20, scale: 0.95 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100
                  }}
                  whileHover={{ y: -4, transition: { duration: 0.2 } }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
                >
                  {/* Order Header */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-12 h-12 rounded-xl flex items-center justify-center"
                          style={{ backgroundColor: '#8F3DD2' }}
                        >
                          <Package className="text-white" size={24} />
                        </div>
                        <div>
                          <h3 className="font-bold text-lg text-gray-900">#{order.id.slice(-8)}</h3>
                          <p className="text-gray-600 text-sm">{order.supplier.name}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          {order.status}
                        </div>
                        <p className="text-gray-500 text-xs mt-1">
                          {formatDate(order.createdAt).date} • {formatDate(order.createdAt).time}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Order Details */}
                  <div className="p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      {/* Left Column - Items & Info */}
                      <div className="space-y-4">
                        {/* Items */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <Package size={16} />
                            Order Items
                          </h4>
                          <div className="space-y-2">
                            {order.items.map((item, idx) => (
                              <div key={idx} className="flex justify-between items-center py-2 px-3 bg-gray-50 rounded-lg">
                                <span className="text-gray-700">
                                  {item.qty}× {item.product.name}
                                </span>
                                <span className="font-medium text-gray-900">
                                  ₪{(item.finalPrice * item.qty).toFixed(2)}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Delivery Info */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <MapPin size={16} />
                            Delivery Details
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center gap-2 text-gray-600">
                              <MapPin size={14} />
                              <span>{order.address}</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-600">
                              <Phone size={14} />
                              <span>{order.phone}</span>
                            </div>
                            <div className="flex items-center gap-2 text-gray-600">
                              <Clock size={14} />
                              <span>Est. {order.estimatedTime}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right Column - Payment & Driver */}
                      <div className="space-y-4">
                        {/* Payment Info */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <CreditCard size={16} />
                            Payment
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-gray-600">Payment Method:</span>
                              <span className="font-medium capitalize">{order.paymentMethod}</span>
                            </div>
                            <div className="flex justify-between items-center text-lg font-bold">
                              <span>Total:</span>
                              <span className="text-primary-600">₪{order.total.toFixed(2)}</span>
                            </div>
                          </div>
                        </div>

                        {/* Driver Info */}
                        {order.driverName && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <Truck size={16} />
                              Driver
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-gray-600">Name:</span>
                                <span className="font-medium">{order.driverName}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600">Phone:</span>
                                <span className="font-medium">{order.driverPhone}</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="mt-6 pt-4 border-t border-gray-100">
                      <div className="flex flex-col sm:flex-row gap-3">
                        <button
                          onClick={() => navigate(`/customer/order-details?orderId=${order.id}`)}
                          className="flex-1 px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                        >
                          <Eye size={18} />
                          View Details
                        </button>
                        {(order.status === 'Preparing' || order.status === 'On the Way') && (
                          <button
                            onClick={() => navigate(`/customer/order-tracking?orderId=${order.id}`)}
                            className="flex-1 px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                          >
                            <Navigation size={18} />
                            Track Order
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default OrdersPage;
