import { Request, Response } from 'express';
import { Category } from '../models/Category';
import { validationResult } from 'express-validator';

export class CategoryController {
  // Get all active categories
  static async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await Category.find({ isActive: true })
        .sort({ order: 1, createdAt: 1 })
        .select('-__v');

      res.json({
        success: true,
        data: categories
      });
    } catch (error) {
      console.error('Error fetching categories:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch categories',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get category by key
  static async getCategoryByKey(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;
      
      const category = await Category.findOne({ key, isActive: true })
        .select('-__v');

      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      res.json({
        success: true,
        data: category
      });
    } catch (error) {
      console.error('Error fetching category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Create new category (admin only)
  static async createCategory(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { key, label, icon, color, route, description, order } = req.body;

      // Check if category with this key already exists
      const existingCategory = await Category.findOne({ key });
      if (existingCategory) {
        res.status(409).json({
          success: false,
          message: 'Category with this key already exists'
        });
        return;
      }

      const category = new Category({
        key,
        label,
        icon,
        color,
        route,
        description,
        order: order || 0
      });

      await category.save();

      res.status(201).json({
        success: true,
        message: 'Category created successfully',
        data: category
      });
    } catch (error) {
      console.error('Error creating category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update category (admin only)
  static async updateCategory(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { key } = req.params;
      const updateData = req.body;

      const category = await Category.findOneAndUpdate(
        { key },
        updateData,
        { new: true, runValidators: true }
      ).select('-__v');

      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Category updated successfully',
        data: category
      });
    } catch (error) {
      console.error('Error updating category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Delete category (admin only)
  static async deleteCategory(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;

      const category = await Category.findOneAndDelete({ key });

      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Category deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete category',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Toggle category active status (admin only)
  static async toggleCategoryStatus(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;

      const category = await Category.findOne({ key });

      if (!category) {
        res.status(404).json({
          success: false,
          message: 'Category not found'
        });
        return;
      }

      category.isActive = !category.isActive;
      await category.save();

      res.json({
        success: true,
        message: `Category ${category.isActive ? 'activated' : 'deactivated'} successfully`,
        data: category
      });
    } catch (error) {
      console.error('Error toggling category status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to toggle category status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
