import React, { useState } from 'react';
import { YStack, Text, H5, XStack, Switch, Checkbox } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { Ionicons } from '@expo/vector-icons';
import { Pressable, Alert } from 'react-native';
import { router } from 'expo-router';
import * as Location from 'expo-location';
import { useTranslation } from 'react-i18next';

export const FinalStep = () => {
  const { t } = useTranslation();
  const { signupData, updateSignupData, resetSignup } = useSignupStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        updateSignupData({ 
          location: [location.coords.longitude, location.coords.latitude] 
        });
        Alert.alert(t('common.success', { defaultValue: 'Success' }), t('signup.finalStep.locationGranted', { defaultValue: 'Location access granted!' }));
      } else {
        Alert.alert(t('signup.finalStep.permissionDenied', { defaultValue: 'Permission Denied' }), t('signup.finalStep.locationOptional', { defaultValue: 'Location access is optional but recommended for better service.' }));
      }
    } catch (error) {
      console.error('Location error:', error);
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('signup.finalStep.locationError', { defaultValue: 'Could not get location. You can set it later in your profile.' }));
    }
  };

  const handleCompleteSignup = async () => {
    if (!signupData.terms) {
      Alert.alert(t('auth.termsRequired', { defaultValue: 'Terms Required' }), t('auth.termsRequired', { defaultValue: 'Please accept the terms and conditions to continue.' }));
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Here you would typically send the data to your backend
      console.log('Signup data:', signupData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        t('common.success', { defaultValue: 'Success!' }),
        t('auth.accountCreated', { defaultValue: 'Your account has been created successfully. Welcome to BolTalab - Fast As Lightning! ⚡' }),
        [
          {
            text: t('common.continue', { defaultValue: 'Continue' }),
            onPress: () => {
              resetSignup();
              router.replace('/authentication/login');
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('signup.errors.unexpectedError', { defaultValue: 'Something went wrong. Please try again.' }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="checkmark-circle" size={48} color="#10b981" />
        <H5 textAlign="center" color="$primary">{t('signup.finalStep.title', { defaultValue: 'Almost Done!' })}</H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          {t('signup.finalStep.subtitle', { defaultValue: 'Just a few final preferences and you\'re all set' })}
        </Text>
      </YStack>

      <YStack gap="$4">
        {/* Location Permission */}
        <YStack gap="$3" padding="$4" backgroundColor="$gray2" borderRadius="$4">
          <XStack alignItems="center" justifyContent="space-between">
            <YStack flex={1} gap="$1">
              <XStack alignItems="center" gap="$2">
                <Ionicons name="location" size={20} color="#7529B3" />
                <Text fontSize="$4" fontWeight="600" color="$gray11">
                  {t('signup.finalStep.enableLocation', { defaultValue: 'Location Access' })}
                </Text>
              </XStack>
              <Text fontSize="$3" color="$gray9">
                {signupData.location
                  ? t('signup.finalStep.locationGranted', { defaultValue: 'Location access granted ✓' })
                  : t('signup.finalStep.locationDescription', { defaultValue: 'Help us provide better service by sharing your location' })
                }
              </Text>
            </YStack>
            {!signupData.location && (
              <Pressable
                onPress={handleLocationPermission}
                style={{
                  backgroundColor: '#7529B3',
                  paddingHorizontal: 16,
                  paddingVertical: 8,
                  borderRadius: 8,
                }}
              >
                <Text color="white" fontSize="$3" fontWeight="500">
                  {t('common.allow', { defaultValue: 'Allow' })}
                </Text>
              </Pressable>
            )}
          </XStack>
        </YStack>

        {/* Notifications */}
        <XStack alignItems="center" justifyContent="space-between" padding="$4" backgroundColor="$gray2" borderRadius="$4">
          <YStack flex={1} gap="$1">
            <XStack alignItems="center" gap="$2">
              <Ionicons name="notifications" size={20} color="#7529B3" />
              <Text fontSize="$4" fontWeight="600" color="$gray11">
                {t('signup.finalStep.enableNotifications', { defaultValue: 'Push Notifications' })}
              </Text>
            </XStack>
            <Text fontSize="$3" color="$gray9">
              {t('signup.finalStep.notificationDescription', { defaultValue: 'Get notified about orders, offers, and updates' })}
            </Text>
          </YStack>
          <Switch
            checked={signupData.notifications}
            onCheckedChange={(checked) => updateSignupData({ notifications: checked })}
            backgroundColor={signupData.notifications ? '$green8' : '$gray6'}
          />
        </XStack>

        {/* Terms and Conditions */}
        <Pressable
          onPress={() => updateSignupData({ terms: !signupData.terms })}
          style={{
            padding: 16,
            backgroundColor: '#f9f9f9',
            borderRadius: 12,
            borderWidth: 2,
            borderColor: signupData.terms ? '#10b981' : '#e5e5e5',
          }}
        >
          <XStack alignItems="flex-start" gap="$3">
            <Ionicons 
              name={signupData.terms ? "checkbox" : "square-outline"} 
              size={24} 
              color={signupData.terms ? '#10b981' : '#666'} 
            />
            <YStack flex={1} gap="$1">
              <Text fontSize="$4" fontWeight="600" color="$gray11">
                {t('auth.terms', { defaultValue: 'Terms & Conditions' })} *
              </Text>
              <Text fontSize="$3" color="$gray9">
                {t('signup.finalStep.agreeToTerms', { defaultValue: 'I agree to the Terms of Service and Privacy Policy. I understand that my data will be processed according to these terms.' })}
              </Text>
            </YStack>
          </XStack>
        </Pressable>
      </YStack>

      {/* Summary */}
      <YStack gap="$3" marginTop="$4" padding="$4" backgroundColor="$blue2" borderRadius="$4">
        <Text fontSize="$4" fontWeight="600" color="$blue11">
          {t('signup.finalStep.accountSummary', { defaultValue: 'Account Summary' })}
        </Text>
        <YStack gap="$2">
          <Text fontSize="$3" color="$blue10">
            <Text fontWeight="600">{t('signup.finalStep.name', { defaultValue: 'Name' })}:</Text> {signupData.firstName} {signupData.lastName}
          </Text>
          <Text fontSize="$3" color="$blue10">
            <Text fontWeight="600">{t('signup.finalStep.email', { defaultValue: 'Email' })}:</Text> {signupData.email}
          </Text>
          <Text fontSize="$3" color="$blue10">
            <Text fontWeight="600">{t('signup.finalStep.accountType', { defaultValue: 'Account Type' })}:</Text> {signupData.userType === 'customer' ? t('signup.finalStep.customer', { defaultValue: 'Customer' }) : t('signup.finalStep.supplier', { defaultValue: 'Supplier' })}
          </Text>
          {signupData.userType === 'supplier' && signupData.storeName && (
            <Text fontSize="$3" color="$blue10">
              <Text fontWeight="600">{t('signup.finalStep.business', { defaultValue: 'Business' })}:</Text> {signupData.storeName}
            </Text>
          )}
        </YStack>
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          {t('signup.finalStep.completingSignup', { defaultValue: 'By completing signup, you agree to our terms and conditions' })}
        </Text>
      </YStack>
    </YStack>
  );
};
