{"cesVersion": "2.18.3", "projectName": "wasel", "packages": [{"name": "expo-router", "type": "navigation", "options": {"type": "stack"}}, {"name": "tamagui", "type": "styling"}, {"name": "zustand", "type": "state-management"}, {"name": "firebase", "type": "authentication"}], "flags": {"noGit": false, "noInstall": false, "overwrite": false, "importAlias": true, "packageManager": "npm", "eas": true, "publish": false}, "packageManager": {"type": "npm", "version": "10.9.2"}, "os": {"type": "Windows_NT", "platform": "win32", "arch": "x64", "kernelVersion": "10.0.19045"}}