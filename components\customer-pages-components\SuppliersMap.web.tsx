import { useState, useEffect } from 'react';
import { Dimensions, Pressable } from 'react-native';
import { View, Text, Card, Button, YStack } from 'tamagui';
import { MotiView } from 'moti';
import { suppliersData } from '~/temp-data/suppliersData';
import { router } from 'expo-router';
import { Image } from 'react-native';
// Use react-native-web-maps directly for web
import MapView, { Marker } from 'react-native-web-maps';
import * as Location from 'expo-location';

type Addition = { id: string; name: string; price: number }

type Product = {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  }

type Supplier = {
  id: string;
  name: string;
  logoUrl: string;
  category: string;
  lat: number;
  lng: number;
  products: Product[];
}

export default function SuppliersMap() {
  const [selected, setSelected] = useState<Supplier | null>(null);
  const { width, height } = Dimensions.get('window');
  const [currentRegion, setCurrentRegion] = useState({
    latitude: 32.2211,
    longitude: 35.2544,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  // Get user's current location on component mount
  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission denied, using default location (Nablus)');
        return;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      setCurrentRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      });
    } catch (error) {
      console.error('Error getting current location:', error);
      // Keep default location (Nablus) if location fails
    }
  };

  return (
    <View flex={1}>
      {/* Web Map using react-native-web-maps */}
      <MapView
        style={{ width, height }}
        region={currentRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
        onRegionChangeComplete={setCurrentRegion}
      >
        {/* Supplier Markers */}
        {suppliersData.map((sup, index) => (
          <Marker
            key={sup.id}
            coordinate={{
              latitude: sup.lat,
              longitude: sup.lng,
            }}
            onPress={() => setSelected(sup)}
          >
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                overflow: 'hidden',
                borderWidth: 2,
                borderColor: '#fff',
                backgroundColor: '#eee',
                justifyContent: 'center',
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 5,
              }}
            >
              <Image
                source={{ uri: sup.logoUrl }}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
              />
            </View>
          </Marker>
        ))}
      </MapView>

      {selected && (
        <MotiView
          from={{ opacity: 0, translateY: 50 }}
          animate={{ opacity: 1, translateY: 0 }}
          transition={{ type: 'timing' }}
          style={{ position: 'absolute', bottom: 80, left: 20, right: 20 }}
        >
          <Card p="$4" br="$6" elevate bw={1} bc="$gray5" bg="white">
            <YStack gap="$2">
              <Text fontSize="$6" fontWeight="700">{selected.name}</Text>
              <Text fontSize="$4" color="$gray9">{selected.category}</Text>
              <Button onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: selected.id }
              })}>
                View
              </Button>
              <Button variant="outlined" onPress={() => setSelected(null)}>Close</Button>
            </YStack>
          </Card>
        </MotiView>
      )}
    </View>
  );
}
