import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import L from 'leaflet';
import {
  MapPin,
  Star,
  Clock,
  Phone,
  Eye,
  Navigation,
  Filter,
  Search,
  X,
  Locate
} from 'lucide-react';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Create custom marker with supplier logo
const createCustomMarker = (supplier: typeof mockSuppliers[0]) => {
  const categoryColor = getCategoryColor(supplier.category);
  const statusColor = supplier.isOpen ? '#22c55e' : '#ef4444';

  return L.divIcon({
    html: `
      <div style="
        width: 50px;
        height: 60px;
        position: relative;
      ">
        <!-- Main marker circle -->
        <div style="
          width: 50px;
          height: 50px;
          background: ${categoryColor};
          border: 4px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 10px rgba(0,0,0,0.3);
          position: relative;
        ">
          <!-- Perfectly centered image -->
          <img
            src="${supplier.logoUrl}"
            alt="${supplier.name}"
            style="
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 36px;
              height: 36px;
              object-fit: cover;
              border-radius: 50%;
              background: white;
              border: 2px solid white;
            "
          />
          <!-- Fallback text (hidden by default, shown if image fails) -->
          <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: ${categoryColor};
            font-weight: bold;
            font-size: 18px;
            border: 2px solid white;
            z-index: -1;
          ">
            ${supplier.name.charAt(0).toUpperCase()}
          </div>
        </div>

        <!-- Pointer triangle -->
        <div style="
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 10px solid ${categoryColor};
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        "></div>

        <!-- Status indicator -->
        <div style="
          position: absolute;
          top: -2px;
          right: -2px;
          width: 16px;
          height: 16px;
          background: ${statusColor};
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        "></div>
      </div>
    `,
    className: 'supplier-marker',
    iconSize: [50, 60],
    iconAnchor: [25, 50],
    popupAnchor: [0, -50]
  });
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'restaurant': return '#f97316';
    case 'pharmacy': return '#22c55e';
    case 'electronics': return '#3b82f6';
    case 'grocery': return '#8b5cf6';
    default: return '#6b7280';
  }
};

// Mock suppliers data matching mobile structure
const mockSuppliers = [
  {
    id: '1',
    name: 'Al-Quds Restaurant',
    category: 'restaurant',
    lat: 32.2211,
    lng: 35.2544,
    logoUrl: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop',
    rating: 4.8,
    deliveryTime: '25-35 min',
    openHours: '9:00 AM - 11:00 PM',
    phone: '+970599123456',
    tags: ['Fast Food', 'Middle Eastern'],
    isOpen: true
  },
  {
    id: '2',
    name: 'Pizza Palace',
    category: 'restaurant',
    lat: 32.2250,
    lng: 35.2580,
    logoUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=200&fit=crop',
    rating: 4.6,
    deliveryTime: '30-40 min',
    openHours: '11:00 AM - 12:00 AM',
    phone: '+970568987654',
    tags: ['Pizza', 'Italian'],
    isOpen: true
  },
  {
    id: '3',
    name: 'Fresh Pharmacy',
    category: 'pharmacy',
    lat: 32.2180,
    lng: 35.2520,
    logoUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=200&fit=crop',
    rating: 4.9,
    deliveryTime: '15-25 min',
    openHours: '8:00 AM - 10:00 PM',
    phone: '+970597654321',
    tags: ['Medicine', 'Health'],
    isOpen: true
  },
  {
    id: '4',
    name: 'Tech Store',
    category: 'electronics',
    lat: 32.2280,
    lng: 35.2500,
    logoUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=200&fit=crop',
    rating: 4.4,
    deliveryTime: '45-60 min',
    openHours: '9:00 AM - 9:00 PM',
    phone: '+970595956014',
    tags: ['Electronics', 'Gadgets'],
    isOpen: false
  }
];

const categories = [
  { key: 'all', label: 'All Suppliers', color: 'bg-gray-100 text-gray-700' },
  { key: 'restaurant', label: 'Restaurants', color: 'bg-orange-100 text-orange-700' },
  { key: 'pharmacy', label: 'Pharmacies', color: 'bg-green-100 text-green-700' },
  { key: 'electronics', label: 'Electronics', color: 'bg-blue-100 text-blue-700' },
  { key: 'grocery', label: 'Grocery', color: 'bg-purple-100 text-purple-700' }
];

const SuppliersMapPage: React.FC = () => {
  const [selectedSupplier, setSelectedSupplier] = useState<typeof mockSuppliers[0] | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([32.2211, 35.2544]);
  const [isLocating, setIsLocating] = useState(false);

  // Add marker styles - ATTRACTIVE HOVER WITHOUT MOVEMENT
  useEffect(() => {
    const styleId = 'attractive-marker-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .supplier-marker {
          cursor: pointer !important;
          transition: filter 0.3s ease !important;
        }

        .supplier-marker:hover {
          filter: brightness(1.1) saturate(1.2) drop-shadow(0 4px 15px rgba(0,0,0,0.4)) !important;
        }

        .supplier-marker:active {
          filter: brightness(0.95) saturate(1.1) drop-shadow(0 2px 8px rgba(0,0,0,0.5)) !important;
        }

        /* Subtle glow effect on hover for the main circle */
        .supplier-marker:hover > div > div:first-child {
          box-shadow:
            0 2px 10px rgba(0,0,0,0.3),
            0 0 20px rgba(255,255,255,0.3),
            inset 0 1px 0 rgba(255,255,255,0.4) !important;
        }

        /* Enhanced status indicator on hover */
        .supplier-marker:hover > div > div:last-child {
          box-shadow:
            0 1px 4px rgba(0,0,0,0.3),
            0 0 8px rgba(255,255,255,0.5) !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Filter suppliers based on category and search
  const filteredSuppliers = mockSuppliers.filter(supplier => {
    const matchesCategory = selectedCategory === 'all' || supplier.category === selectedCategory;
    const matchesSearch = searchQuery === '' ||
      supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      supplier.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Get user's current location
  const getCurrentLocation = async () => {
    setIsLocating(true);
    try {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const location = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            setUserLocation(location);
            setMapCenter([location.lat, location.lng]);
            setIsLocating(false);
          },
          (error) => {
            console.error('Error getting location:', error);
            setIsLocating(false);
          }
        );
      }
    } catch (error) {
      console.error('Geolocation error:', error);
      setIsLocating(false);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-100/20 to-third-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-secondary-100/20 to-primary-100/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 h-screen flex flex-col">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white shadow-lg border-b border-gray-100 p-4"
        >
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Suppliers Map</h1>
                <p className="text-gray-600">Find suppliers near you</p>
              </div>
              <button
                onClick={getCurrentLocation}
                disabled={isLocating}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center gap-2 disabled:opacity-50"
              >
                {isLocating ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Locate size={18} />
                )}
                {isLocating ? 'Locating...' : 'My Location'}
              </button>
            </div>

            {/* Search Bar */}
            <div className="mb-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={20} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search suppliers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    <X size={20} className="text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setSelectedCategory(category.key)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedCategory === category.key
                      ? 'bg-primary-600 text-white shadow-lg scale-105'
                      : category.color + ' hover:scale-105'
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Real Map Container */}
        <div className="flex-1 relative">
          <MapContainer
            center={mapCenter}
            zoom={13}
            style={{ height: '100%', width: '100%' }}
            className="rounded-lg"
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />

            {/* User Location Marker */}
            {userLocation && (
              <Marker position={[userLocation.lat, userLocation.lng]}>
                <Popup>
                  <div className="text-center">
                    <strong>Your Location</strong>
                  </div>
                </Popup>
              </Marker>
            )}

            {/* Supplier Markers */}
            {filteredSuppliers.map((supplier) => (
              <Marker
                key={supplier.id}
                position={[supplier.lat, supplier.lng]}
                icon={createCustomMarker(supplier)}
                eventHandlers={{
                  click: () => setSelectedSupplier(supplier),
                }}
              >
                <Popup>
                  <div className="text-center min-w-[200px]">
                    <div className="flex items-center gap-2 mb-2">
                      <img
                        src={supplier.logoUrl}
                        alt={supplier.name}
                        className="w-8 h-8 rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                      <div>
                        <h3 className="font-bold text-sm">{supplier.name}</h3>
                        <p className="text-xs text-gray-600 capitalize">{supplier.category}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
                      <div className="flex items-center gap-1">
                        <Star size={12} className="text-yellow-500 fill-current" />
                        <span>{supplier.rating}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock size={12} />
                        <span>{supplier.deliveryTime}</span>
                      </div>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      supplier.isOpen
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    }`}>
                      {supplier.isOpen ? 'Open' : 'Closed'}
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>

          {/* Legend Overlay */}
          <div className="absolute bottom-4 left-4 bg-white rounded-xl shadow-lg p-4 z-[1000]">
            <h4 className="font-semibold text-gray-900 mb-2">Legend</h4>
            <div className="space-y-2">
              {categories.slice(1).map((category) => (
                <div key={category.key} className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{
                      backgroundColor: category.key === 'restaurant' ? '#f97316' :
                                     category.key === 'pharmacy' ? '#22c55e' :
                                     category.key === 'electronics' ? '#3b82f6' :
                                     category.key === 'grocery' ? '#8b5cf6' : '#6b7280'
                    }}
                  ></div>
                  <span className="text-sm text-gray-700">{category.label}</span>
                </div>
              ))}
              <div className="flex items-center gap-2 pt-2 border-t border-gray-200">
                <div className="w-4 h-4 bg-blue-500 rounded-full border border-gray-300"></div>
                <span className="text-sm text-gray-700">Your Location</span>
              </div>
            </div>
          </div>
        </div>

        {/* Supplier Details Modal */}
        <AnimatePresence>
          {selectedSupplier && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-end md:items-center justify-center p-4"
              onClick={() => setSelectedSupplier(null)}
            >
              <motion.div
                initial={{ y: 100, opacity: 0, scale: 0.95 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: 100, opacity: 0, scale: 0.95 }}
                transition={{ type: "spring", damping: 25, stiffness: 300 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[85vh] overflow-hidden flex flex-col"
              >
                {/* Supplier Header - Fixed */}
                <div className="relative flex-shrink-0">
                  <img
                    src={selectedSupplier.banner}
                    alt={selectedSupplier.name}
                    className="w-full h-32 object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=200&fit=crop';
                    }}
                  />
                  <div className="absolute top-4 right-4">
                    <button
                      onClick={() => setSelectedSupplier(null)}
                      className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors"
                    >
                      <X size={16} className="text-gray-600" />
                    </button>
                  </div>
                  <div className="absolute -bottom-6 left-4">
                    <div className="w-12 h-12 bg-white rounded-xl border-2 border-white shadow-lg overflow-hidden">
                      <img
                        src={selectedSupplier.logoUrl}
                        alt={selectedSupplier.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="w-full h-full flex items-center justify-center text-gray-600 font-bold text-sm">
                              ${selectedSupplier.name.charAt(0)}
                            </div>
                          `;
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Supplier Details - Scrollable */}
                <div className="flex-1 overflow-y-auto p-6 pt-8 modal-scroll">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-1">{selectedSupplier.name}</h3>
                      <p className="text-gray-600 capitalize mb-2">{selectedSupplier.category}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Star size={14} className="text-yellow-500 fill-current" />
                          <span className="font-medium">{selectedSupplier.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={14} />
                          <span>{selectedSupplier.deliveryTime}</span>
                        </div>
                      </div>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                      selectedSupplier.isOpen
                        ? 'bg-green-100 text-green-700'
                        : 'bg-red-100 text-red-700'
                    }`}>
                      {selectedSupplier.isOpen ? 'Open' : 'Closed'}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {selectedSupplier.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Contact Info */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Clock size={16} className="text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Opening Hours</p>
                        <p className="font-medium">{selectedSupplier.openHours}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Phone size={16} className="text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Phone</p>
                        <p className="font-medium">{selectedSupplier.phone}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        <MapPin size={16} className="text-gray-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Location</p>
                        <p className="font-medium">Nablus, Palestine</p>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        // Navigate to supplier details
                        window.location.href = `/customer/supplier-details/${selectedSupplier.id}`;
                      }}
                      disabled={!selectedSupplier.isOpen}
                      className="flex-1 px-4 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Eye size={18} />
                      {selectedSupplier.isOpen ? 'View Menu' : 'Closed'}
                    </button>
                    <button
                      onClick={() => {
                        // Navigate to location
                        const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedSupplier.lat},${selectedSupplier.lng}`;
                        window.open(url, '_blank');
                      }}
                      className="px-4 py-3 bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                    >
                      <Navigation size={18} />
                      Directions
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SuppliersMapPage;
