import { Router } from 'express';
import { body } from 'express-validator';
import { CategoryController } from '../controllers/categoryController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for category creation/update
const categoryValidation = [
  body('key')
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Key must be a string between 1 and 50 characters'),
  body('label')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Label must be a string between 1 and 100 characters'),
  body('icon')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Icon must be a string between 1 and 100 characters'),
  body('color')
    .isString()
    .trim()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex color code'),
  body('route.pathname')
    .isString()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Route pathname must be a string between 1 and 200 characters'),
  body('route.params.category')
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Route category parameter must be a string between 1 and 50 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be a string with maximum 500 characters'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
];

// Public routes
router.get('/', CategoryController.getCategories);
router.get('/:key', CategoryController.getCategoryByKey);

// Protected routes (admin only)
router.post('/', authenticate, categoryValidation, CategoryController.createCategory);
router.put('/:key', authenticate, categoryValidation, CategoryController.updateCategory);
router.delete('/:key', authenticate, CategoryController.deleteCategory);
router.patch('/:key/toggle-status', authenticate, CategoryController.toggleCategoryStatus);

export default router;
