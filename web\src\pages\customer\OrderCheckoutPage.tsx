import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ArrowLeft, MapPin, Phone, CreditCard, Truck, Edit3 } from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { useOrdersStore } from '../../stores/ordersStore';
import { createOrder } from '../../../../services/apiService';

const OrderCheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { clearCart } = useCartStore();
  const { addMultipleOrders } = useOrdersStore();

  // Get cart data from location state or use current cart
  const cartData = location.state || {};
  const { itemsBySupplier = {}, totalWithoutFee = 0 } = cartData;

  // If no cart data in location state, redirect back
  const supplierIds = Object.keys(itemsBySupplier);
  if (supplierIds.length === 0) {
    // Could redirect to home or show empty cart message
  }

  // Form states
  const [address, setAddress] = useState('Nablus, Palestine');
  const [phone, setPhone] = useState('');
  const [notes, setNotes] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [cardNumber, setCardNumber] = useState('');
  const [cardCvv, setCardCvv] = useState('');
  const [promoCode, setPromoCode] = useState('');

  // Calculate totals
  const deliveryFee = 12;
  const promoDiscount = promoCode === 'WASEL10' ? 10 : 0;

  const subtotal = Object.values(itemsBySupplier).flat().reduce((sum, item) => {
    return sum + (item.finalPrice * item.qty);
  }, 0);

  const total = subtotal + deliveryFee - promoDiscount;

  const isFormValid = () => {
    return address.trim() && 
           phone.trim() && 
           (paymentMethod === 'cash' || (cardNumber.trim() && cardCvv.trim()));
  };

  const handlePlaceOrder = async () => {
    if (!isFormValid()) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      // Create order data for each supplier
      const orders = await Promise.all(
        supplierIds.map(async (supplierId) => {
          const supplierItems = itemsBySupplier[supplierId];
          const supplier = {
            id: supplierId,
            name: supplierItems[0]?.supplierName || 'Supplier'
          };

          const orderSubtotal = supplierItems.reduce((sum: number, item: any) => sum + (item.finalPrice * item.qty), 0);
          const orderDeliveryFee = deliveryFee / supplierIds.length; // Split delivery fee
          const orderPromoDiscount = promoDiscount / supplierIds.length; // Split discount
          const orderTotal = orderSubtotal + orderDeliveryFee - orderPromoDiscount;

          // Prepare order data for backend
          const orderData = {
            supplierId,
            supplierName: supplier.name,
            items: supplierItems.map((item: any) => ({
              productId: item.product.id || item.id,
              productName: item.product.name || item.name,
              quantity: item.qty,
              price: item.finalPrice,
              options: {
                selectedAdditions: item.selectedAdditions,
                selectedSides: item.selectedSides,
                without: item.without,
                selectedSize: item.selectedSize,
                selectedColor: item.selectedColor
              }
            })),
            subtotal: orderSubtotal,
            deliveryFee: orderDeliveryFee,
            totalAmount: orderTotal,
            paymentMethod: paymentMethod as 'cash' | 'card' | 'wallet',
            deliveryAddress: {
              street: address,
              city: 'Nablus', // Default city
              coordinates: {
                lat: 32.2211, // Default coordinates for Nablus
                lng: 35.2544
              },
              notes: notes
            },
            notes: notes
          };

          // Create order via backend API
          const createdOrder = await createOrder(orderData);

          // Return order in local format for immediate UI update
          return {
            id: createdOrder.orderId,
            createdAt: new Date().toISOString(),
            items: supplierItems.map((item: any) => ({
              product: item.product,
              qty: item.qty,
              finalPrice: item.finalPrice,
              selectedAdditions: item.selectedAdditions,
              selectedSides: item.selectedSides,
              without: item.without,
              selectedSize: item.selectedSize,
              selectedColor: item.selectedColor
            })),
            supplier,
            subtotal: orderSubtotal,
            deliveryFee: orderDeliveryFee,
            promoDiscount: orderPromoDiscount,
            total: orderTotal,
            status: 'Pending' as const,
            address,
            phone,
            notes,
            paymentMethod,
            promoCode,
            estimatedTime: '45-60 mins',
            placedAt: new Date().toISOString()
          };
        })
      );

      // Add orders to the orders store
      addMultipleOrders(orders);

      // Clear cart after successful order
      clearCart();

      // Navigate to confirmation page with all orders
      navigate('/customer/order-confirmation', { state: { allOrders: orders } });
    } catch (error) {
      console.error('Error placing order:', error);
      alert('Failed to place order. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">Checkout</h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Order Summary</h2>
                <button
                  onClick={() => navigate(-1)}
                  className="text-purple-600 hover:text-purple-700 flex items-center gap-1 text-sm"
                >
                  <Edit3 className="w-4 h-4" />
                  Edit Cart
                </button>
              </div>
              
              <div className="space-y-4">
                {supplierIds.map(supplierId => {
                  const supplierItems = itemsBySupplier[supplierId];
                  const supplierName = supplierItems[0]?.supplierName || 'Supplier';

                  return (
                    <div key={supplierId} className="space-y-3">
                      <div className="font-medium text-gray-900">From: {supplierName}</div>
                      {supplierItems.map((item) => {
                        const formatAdditions = () => {
                          const additions = [];
                          if (item.selectedAdditions?.length > 0) {
                            additions.push(`+${item.selectedAdditions.map((a: any) => a.name).join(', ')}`);
                          }
                          if (item.selectedSides?.length > 0) {
                            additions.push(`Sides: ${item.selectedSides.map((s: any) => s.name).join(', ')}`);
                          }
                          if (item.without?.length > 0) {
                            additions.push(`Without: ${item.without.join(', ')}`);
                          }
                          return additions.length > 0 ? ` (${additions.join(' • ')})` : '';
                        };

                        return (
                          <div key={item.id} className="flex justify-between items-start py-2">
                            <div>
                              <span className="font-medium">{item.qty} × {item.product.name}</span>
                              {formatAdditions() && (
                                <div className="text-sm text-gray-600">{formatAdditions()}</div>
                              )}
                            </div>
                            <span className="font-medium">₪{(item.finalPrice * item.qty).toFixed(2)}</span>
                          </div>
                        );
                      })}
                      {supplierIds.length > 1 && (
                        <div className="border-b border-gray-200 pb-2"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Delivery Address */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Delivery Address
              </h2>
              <textarea
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                placeholder="Enter your full address..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                rows={3}
                required
              />
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Phone className="w-5 h-5" />
                Contact Information
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="+970 XXX XXX XXX"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Instructions (Optional)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Any special requests or notes for the driver..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={2}
                  />
                </div>
              </div>
            </div>

            {/* Payment Method */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CreditCard className="w-5 h-5" />
                Payment Method
              </h2>
              
              <div className="space-y-4">
                <div className="flex gap-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="payment"
                      value="cash"
                      checked={paymentMethod === 'cash'}
                      onChange={(e) => setPaymentMethod(e.target.value as 'cash')}
                      className="w-4 h-4 text-purple-600"
                    />
                    <span>Cash on Delivery</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="payment"
                      value="card"
                      checked={paymentMethod === 'card'}
                      onChange={(e) => setPaymentMethod(e.target.value as 'card')}
                      className="w-4 h-4 text-purple-600"
                    />
                    <span>Credit Card</span>
                  </label>
                </div>

                {paymentMethod === 'card' && (
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Card Number *
                      </label>
                      <input
                        type="text"
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        placeholder="1234 5678 9012 3456"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CVV *
                      </label>
                      <input
                        type="text"
                        value={cardCvv}
                        onChange={(e) => setCardCvv(e.target.value)}
                        placeholder="123"
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        required
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Order Total Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Total</h3>
              
              <div className="space-y-3 mb-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>₪{subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="flex items-center gap-1">
                    <Truck className="w-4 h-4" />
                    Delivery Fee
                  </span>
                  <span>₪{deliveryFee.toFixed(2)}</span>
                </div>
                {promoDiscount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Promo Discount</span>
                    <span>-₪{promoDiscount.toFixed(2)}</span>
                  </div>
                )}
                <hr />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total</span>
                  <span>₪{total.toFixed(2)}</span>
                </div>
              </div>

              {/* Promo Code */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Promo Code
                </label>
                <input
                  type="text"
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value)}
                  placeholder="Enter promo code"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
                {promoCode === 'WASEL10' && (
                  <p className="text-green-600 text-sm mt-1">✓ Promo code applied!</p>
                )}
              </div>

              <button
                onClick={handlePlaceOrder}
                disabled={!isFormValid()}
                className={`w-full py-3 rounded-lg font-semibold transition-colors ${
                  isFormValid()
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Place Order - ₪{total.toFixed(2)}
              </button>

              <p className="text-xs text-gray-500 mt-3 text-center">
                Estimated delivery: 45-60 minutes
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCheckoutPage;
