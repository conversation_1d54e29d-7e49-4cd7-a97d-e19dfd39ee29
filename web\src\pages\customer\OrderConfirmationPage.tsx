import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, Clock, MapPin, Phone, CreditCard, Package, Home } from 'lucide-react';
import { motion } from 'framer-motion';

const OrderConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get all orders from location state or use mock data
  const allOrders = location.state?.allOrders || [
    {
      id: `W-${Date.now().toString().slice(-6)}-1`,
      items: [
        { product: { name: 'Chicken Shawarma' }, qty: 2, finalPrice: 25 },
        { product: { name: 'French Fries' }, qty: 1, finalPrice: 12 }
      ],
      supplier: { name: 'Al-Quds Restaurant' },
      total: 62,
      estimatedTime: '45-60 mins',
      address: 'Nablus, Palestine',
      phone: '+970568406041',
      paymentMethod: 'cash',
      status: 'Pending',
      placedAt: new Date().toISOString()
    }
  ];

  // Calculate total across all orders
  const grandTotal = allOrders.reduce((sum: number, order: any) => sum + order.total, 0);
  const totalOrders = allOrders.length;

  const handleTrackOrder = (orderId: string) => {
    navigate(`/customer/order-tracking?orderId=${orderId}`);
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewOrders = () => {
    navigate('/customer/orders');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-pink-600">
      {/* Header Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {totalOrders > 1 ? 'Orders Placed Successfully!' : 'Order Placed Successfully!'}
            </h1>
            <p className="text-xl text-white opacity-90">
              {totalOrders > 1
                ? `Thank you! We've received ${totalOrders} orders from different suppliers.`
                : 'Thank you for your order. We\'re preparing it for you now.'
              }
            </p>
            {totalOrders > 1 && (
              <p className="text-lg text-white opacity-80 mt-2">
                Total: ₪{grandTotal.toFixed(2)}
              </p>
            )}
          </motion.div>
        </div>
      </div>

      {/* Content Section */}
      <div className="relative -mt-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex justify-center mb-8"
          >
            <div className="bg-white rounded-full p-6 shadow-lg">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
          </motion.div>

          {/* Order Details Cards */}
          <div className="space-y-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 text-center">
              {totalOrders > 1 ? `Your ${totalOrders} Orders` : 'Order Details'}
            </h2>

            {allOrders.map((order: any, orderIndex: number) => (
              <motion.div
                key={order.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 + (orderIndex * 0.1) }}
                className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-bold text-gray-900">
                      Order #{orderIndex + 1}
                    </h3>
                    <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                      {order.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Order Info */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Package className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-semibold text-gray-900">Order ID</p>
                          <p className="text-gray-600">{order.id}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <Clock className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-semibold text-gray-900">Estimated Time</p>
                          <p className="text-gray-600">{order.estimatedTime}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <MapPin className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-semibold text-gray-900">Delivery Address</p>
                          <p className="text-gray-600">{order.address}</p>
                        </div>
                      </div>
                    </div>

                    {/* Contact & Payment */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Phone className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-semibold text-gray-900">Contact Number</p>
                          <p className="text-gray-600">{order.phone}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <CreditCard className="w-5 h-5 text-purple-600" />
                        <div>
                          <p className="font-semibold text-gray-900">Payment Method</p>
                          <p className="text-gray-600 capitalize">{order.paymentMethod}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">₪</span>
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">Total Amount</p>
                          <p className="text-gray-600 font-bold">₪{order.total.toFixed(2)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-4">Order Items</h4>
                    <div className="space-y-3">
                      <div className="font-medium text-purple-600">From: {order.supplier.name}</div>
                      {order.items.map((item: any, index: number) => (
                        <div key={index} className="flex justify-between items-center py-2">
                          <span className="text-gray-700">
                            {item.qty} × {item.product.name}
                          </span>
                          <span className="font-medium text-gray-900">
                            ₪{(item.finalPrice * item.qty).toFixed(2)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Track Order Button */}
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => handleTrackOrder(order.id)}
                      className="w-full bg-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                    >
                      <Clock className="w-5 h-5" />
                      Track This Order
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            <button
              onClick={handleViewOrders}
              className="bg-purple-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
            >
              <Package className="w-5 h-5" />
              View All Orders
            </button>

            <button
              onClick={handleGoHome}
              className="bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
            >
              <Home className="w-5 h-5" />
              Back to Home
            </button>
          </motion.div>

          {/* Additional Info */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-8 text-center"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-2">What happens next?</h3>
              <div className="text-gray-600 space-y-2">
                {totalOrders > 1 ? (
                  <>
                    <p>• Your orders have been sent to {totalOrders} different suppliers</p>
                    <p>• Each supplier will prepare their items independently</p>
                    <p>• You'll receive updates for each order separately</p>
                    <p>• Drivers will be assigned to each order when ready</p>
                    <p>• You can track each order individually in your orders page</p>
                  </>
                ) : (
                  <>
                    <p>• Your order has been sent to {allOrders[0].supplier.name}</p>
                    <p>• You'll receive updates via SMS/notifications</p>
                    <p>• A driver will be assigned once your order is ready</p>
                    <p>• You can track your order in real-time</p>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
