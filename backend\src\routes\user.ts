import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authenticate, authorize } from '../middleware/auth';
import {
  validateUpdateProfile,
  validateChangePassword,
} from '../middleware/validation';

const router = Router();

// All routes require authentication
router.use(authenticate);

// User profile routes
router.get('/profile', UserController.getProfile);
router.put('/profile', validateUpdateProfile, UserController.updateProfile);
router.put('/change-password', validateChangePassword, UserController.changePassword);
router.delete('/account', UserController.deleteAccount);

// Admin or self access routes
router.get('/:userId', UserController.getUserById);

export default router;
