import React from 'react';
import { useNavigate } from 'react-router-dom';
import { X, Plus, Minus, Trash2, ShoppingCart } from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { motion, AnimatePresence } from 'framer-motion';

interface CartModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CartModal: React.FC<CartModalProps> = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const { items, removeItem, updateQty, getItemsBySupplier, totalPrice, getTotalItems, getTotalPrice } = useCartStore();
  const itemsBySupplier = getItemsBySupplier();
  const supplierIds = Object.keys(itemsBySupplier);

  const handleCheckout = () => {
    if (getTotalItems() > 0) {
      onClose();
      navigate('/customer/order-checkout', {
        state: { 
          itemsBySupplier,
          totalWithoutFee: getTotalPrice()
        }
      });
    }
  };

  const formatAdditions = (item: any) => {
    const additions = [];
    if (item.selectedAdditions?.length > 0) {
      additions.push(`+${item.selectedAdditions.map((a: any) => a.name).join(', ')}`);
    }
    if (item.selectedSides?.length > 0) {
      additions.push(`Sides: ${item.selectedSides.map((s: any) => s.name).join(', ')}`);
    }
    if (item.without?.length > 0) {
      additions.push(`Without: ${item.without.join(', ')}`);
    }
    if (item.selectedSize) {
      additions.push(`Size: ${item.selectedSize}`);
    }
    if (item.selectedColor) {
      additions.push(`Color: ${item.selectedColor}`);
    }
    return additions.join(' • ');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-full max-w-md bg-white shadow-xl z-50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <ShoppingCart className="w-6 h-6" />
                Your Cart ({getTotalItems()})
              </h2>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto">
              {supplierIds.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center p-6">
                  <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
                  <p className="text-gray-600">Add some items to get started!</p>
                </div>
              ) : (
                <div className="p-6 space-y-6">
                  {supplierIds.map(supplierId => {
                    const supplierItems = itemsBySupplier[supplierId];
                    const supplierName = supplierItems[0]?.supplierName || 'Supplier';
                    
                    return (
                      <div key={supplierId} className="space-y-4">
                        <h3 className="font-semibold text-lg text-gray-900">
                          From: {supplierName}
                        </h3>
                        
                        <div className="space-y-3">
                          {supplierItems.map((item) => (
                            <div key={item.id} className="bg-gray-50 rounded-lg p-4">
                              <div className="flex gap-3">
                                <img
                                  src={item.product.image}
                                  alt={item.product.name}
                                  className="w-16 h-16 object-cover rounded-lg"
                                />
                                
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-gray-900 truncate">
                                    {item.product.name}
                                  </h4>
                                  
                                  {formatAdditions(item) && (
                                    <p className="text-sm text-gray-600 mt-1">
                                      {formatAdditions(item)}
                                    </p>
                                  )}
                                  
                                  <div className="flex items-center justify-between mt-2">
                                    <span className="font-semibold text-purple-600">
                                      ₪{(item.finalPrice * item.qty).toFixed(2)}
                                    </span>
                                    
                                    <div className="flex items-center gap-2">
                                      <div className="flex items-center gap-1">
                                        <button
                                          onClick={() => updateQty(item.id, item.qty - 1)}
                                          className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                                        >
                                          <Minus className="w-4 h-4" />
                                        </button>
                                        
                                        <span className="w-8 text-center font-medium">
                                          {item.qty}
                                        </span>
                                        
                                        <button
                                          onClick={() => updateQty(item.id, item.qty + 1)}
                                          className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                                        >
                                          <Plus className="w-4 h-4" />
                                        </button>
                                      </div>
                                      
                                      <button
                                        onClick={() => removeItem(item.id)}
                                        className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                      >
                                        <Trash2 className="w-4 h-4" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        <div className="flex justify-between items-center pt-3 border-t border-gray-200">
                          <span className="font-semibold text-gray-900">Subtotal:</span>
                          <span className="font-bold text-lg text-purple-600">
                            ₪{totalPrice(supplierId).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Footer */}
            {supplierIds.length > 0 && (
              <div className="border-t border-gray-200 p-6 space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Total:</span>
                  <span className="text-xl font-bold text-purple-600">
                    ₪{getTotalPrice().toFixed(2)}
                  </span>
                </div>
                
                <button
                  onClick={handleCheckout}
                  className="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                >
                  <ShoppingCart className="w-5 h-5" />
                  Proceed to Checkout
                </button>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default CartModal;
