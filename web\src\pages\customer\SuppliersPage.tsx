import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Search, Star, Clock, Phone, MapPin, ArrowLeft } from 'lucide-react';
import { getSuppliersByCategory, Supplier } from '../../../../services/apiService';

// Helper function to check if supplier is currently open
const isSupplierOpen = (openHours: string): boolean => {
  try {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes(); // Current time in minutes

    // Parse openHours format like "10:00 AM - 11:00 PM"
    const [startTime, endTime] = openHours.split(' - ');

    const parseTime = (timeStr: string): number => {
      const [time, period] = timeStr.trim().split(' ');
      const [hours, minutes] = time.split(':').map(Number);
      let totalMinutes = hours * 60 + minutes;

      if (period === 'PM' && hours !== 12) {
        totalMinutes += 12 * 60;
      } else if (period === 'AM' && hours === 12) {
        totalMinutes = minutes;
      }

      return totalMinutes;
    };

    const startMinutes = parseTime(startTime);
    const endMinutes = parseTime(endTime);

    // Handle cases where end time is next day (like 11:00 PM to 2:00 AM)
    if (endMinutes < startMinutes) {
      return currentTime >= startMinutes || currentTime <= endMinutes;
    }

    return currentTime >= startMinutes && currentTime <= endMinutes;
  } catch (error) {
    // If parsing fails, assume open
    return true;
  }
};

const SuppliersPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const category = searchParams.get('category') || 'all';
  const [search, setSearch] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setLoading(true);
        if (category === 'all') {
          // Fetch all suppliers
          const { suppliers: allSuppliers } = await getSuppliersByCategory('');
          setSuppliers(allSuppliers);
        } else {
          // Fetch suppliers by category
          const { suppliers: categorySuppliers } = await getSuppliersByCategory(category);
          setSuppliers(categorySuppliers);
        }
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        setSuppliers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSuppliers();
  }, [category]);

  const filteredSuppliers = useMemo(() => {
    if (search.trim()) {
      return suppliers.filter(supplier =>
        supplier.name.toLowerCase().includes(search.toLowerCase()) ||
        supplier.tags?.some(tag => tag.toLowerCase().includes(search.toLowerCase()))
      );
    }
    return suppliers;
  }, [suppliers, search]);

  // Mock promotions - could be moved to temp data later
  const mockPromotions = [
    { id: '1', title: '20% Off First Order', category: 'restaurants' },
    { id: '2', title: 'Free Delivery', category: 'pharmacies' },
    { id: '3', title: 'Buy 2 Get 1 Free', category: 'clothings' },
    { id: '4', title: 'Fresh Deals Today', category: 'supermarkets' }
  ];

  const filteredPromotions = useMemo(() => {
    if (category === 'all') return mockPromotions;
    return mockPromotions.filter(promo => promo.category === category);
  }, [category]);

  const getCategoryLabel = (cat: string) => {
    const labels: Record<string, string> = {
      'all': 'All Suppliers',
      'restaurants': 'Restaurants',
      'pharmacies': 'Pharmacies',
      'electronics': 'Electronics',
      'grocery': 'Grocery',
      'clothing': 'Clothing',
      'books': 'Books',
      'beauty': 'Beauty',
      'sports': 'Sports'
    };
    return labels[cat] || cat;
  };

  const handleSupplierClick = (supplierId: string) => {
    navigate(`/customer/supplier-details?supplierId=${supplierId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{getCategoryLabel(category)}</h1>
                <div className="flex items-center gap-2 mt-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Nablus, Palestine</span>
                </div>
              </div>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search suppliers…"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Promotions Section */}
        {filteredPromotions.length > 0 && (
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-4">🔥 Promotions</h2>
            <div className="flex gap-4 overflow-x-auto pb-2">
              {filteredPromotions.map((promotion) => (
                <div
                  key={promotion.id}
                  className="flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500 text-white p-4 rounded-lg min-w-[200px]"
                >
                  <h3 className="font-semibold">{promotion.title}</h3>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Suppliers List */}
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-4">Suppliers</h2>
          <div className="space-y-4">
            {filteredSuppliers.map((supplier) => (
              <div
                key={supplier.id}
                onClick={() => handleSupplierClick(supplier.id)}
                className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex">
                  <img
                    src={supplier.logoUrl}
                    alt={supplier.name}
                    className="w-20 h-20 object-cover"
                  />
                  <div className="flex-1 p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900">{supplier.name}</h3>
                        <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span>{supplier.rating}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{supplier.deliveryTime}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Phone className="w-4 h-4" />
                            <span>{supplier.phone}</span>
                          </div>
                        </div>
                        <div className="flex gap-2 mt-2">
                          {supplier.tags.map((tag) => (
                            <span
                              key={tag}
                              className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          isSupplierOpen(supplier.openHours)
                            ? 'bg-green-100 text-green-700'
                            : 'bg-red-100 text-red-700'
                        }`}>
                          {isSupplierOpen(supplier.openHours) ? 'Open' : 'Closed'}
                        </span>
                        <p className="text-xs text-gray-500 mt-1">{supplier.openHours}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredSuppliers.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🏪</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No suppliers found</h3>
              <p className="text-gray-600">Try adjusting your search terms or category</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SuppliersPage;
