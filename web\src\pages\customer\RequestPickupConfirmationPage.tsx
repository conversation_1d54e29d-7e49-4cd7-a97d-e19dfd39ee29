import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, Clock, MapPin, Phone, Package, User, Home } from 'lucide-react';
import { motion } from 'framer-motion';

const RequestPickupConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get pickup request data from location state or use mock data
  const pickupRequest = location.state?.pickupRequest || {
    id: `PU-${Date.now().toString().slice(-6)}`,
    pickupAddress: 'Nablus, Palestine',
    deliveryAddress: 'Ramallah, Palestine',
    senderName: '<PERSON> Samer',
    senderPhone: '+970568406041',
    receiverName: '<PERSON>',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    packageSize: 'small',
    packageWeight: '500g',
    price: 15,
    status: 'Pending',
    preferredTime: 'asap',
    createdAt: new Date().toISOString()
  };

  const handleTrackPickup = () => {
    navigate(`/customer/package-tracking?pickupId=${pickupRequest.id}`);
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewPackages = () => {
    navigate('/customer/packages');
  };

  const getSizeLabel = (size: string) => {
    const labels: Record<string, string> = {
      'small': 'Small (up to 30cm)',
      'medium': 'Medium (30-60cm)',
      'large': 'Large (60-100cm)',
      'extra-large': 'Extra Large (100cm+)'
    };
    return labels[size] || size;
  };

  const getTimeLabel = (time: string) => {
    if (time === 'asap') return 'As soon as possible';
    if (time === 'today') return 'Later today';
    return time;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-600 via-green-700 to-teal-600">
      {/* Header Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Pickup Request Submitted!
            </h1>
            <p className="text-xl text-white opacity-90">
              We'll arrange a driver to pick up your package soon.
            </p>
          </motion.div>
        </div>
      </div>

      {/* Content Section */}
      <div className="relative -mt-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex justify-center mb-8"
          >
            <div className="bg-white rounded-full p-6 shadow-lg">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
          </motion.div>

          {/* Pickup Request Details Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden mb-8"
          >
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Pickup Request Details</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Request Info */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Package className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">Request ID</p>
                      <p className="text-gray-600">{pickupRequest.id}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">Preferred Time</p>
                      <p className="text-gray-600">{getTimeLabel(pickupRequest.preferredTime)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-green-600 mt-1" />
                    <div>
                      <p className="font-semibold text-gray-900">Pickup Address</p>
                      <p className="text-gray-600">{pickupRequest.pickupAddress}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-green-600 mt-1" />
                    <div>
                      <p className="font-semibold text-gray-900">Delivery Address</p>
                      <p className="text-gray-600">{pickupRequest.deliveryAddress}</p>
                    </div>
                  </div>
                </div>

                {/* Contact & Package Info */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <User className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">Sender</p>
                      <p className="text-gray-600">{pickupRequest.senderName}</p>
                      <p className="text-gray-600 text-sm">{pickupRequest.senderPhone}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <User className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">Receiver</p>
                      <p className="text-gray-600">{pickupRequest.receiverName}</p>
                      <p className="text-gray-600 text-sm">{pickupRequest.receiverPhone}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Package className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="font-semibold text-gray-900">Package Details</p>
                      <p className="text-gray-600">{pickupRequest.packageType}</p>
                      <p className="text-gray-600 text-sm">{getSizeLabel(pickupRequest.packageSize)} • {pickupRequest.packageWeight}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">₪</span>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">Service Fee</p>
                      <p className="text-gray-600 font-bold">₪{pickupRequest.price}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900">Status:</span>
                  <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                    {pickupRequest.status}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-4"
          >
            <button
              onClick={handleTrackPickup}
              className="bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
            >
              <Clock className="w-5 h-5" />
              Track Pickup
            </button>
            
            <button
              onClick={handleViewPackages}
              className="bg-white text-green-600 border-2 border-green-600 py-3 px-6 rounded-lg font-semibold hover:bg-green-50 transition-colors flex items-center justify-center gap-2"
            >
              <Package className="w-5 h-5" />
              View All Packages
            </button>
            
            <button
              onClick={handleGoHome}
              className="bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
            >
              <Home className="w-5 h-5" />
              Back to Home
            </button>
          </motion.div>

          {/* Additional Info */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-8 text-center"
          >
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-900 mb-2">What happens next?</h3>
              <div className="text-gray-600 space-y-2">
                <p>• We'll find the nearest available driver</p>
                <p>• You'll receive SMS updates on pickup status</p>
                <p>• Driver will contact you before arrival</p>
                <p>• Track your package in real-time</p>
                <p>• Receiver will be notified upon delivery</p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupConfirmationPage;
