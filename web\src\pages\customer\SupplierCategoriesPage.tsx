import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Search, MapPin, Store, Utensils, Shirt, Pill, ShoppingCart,
  Sparkles, Star, Clock, ChevronRight, X, Crown, Shield, ArrowLeft
} from 'lucide-react';
import { categories } from '../../../../temp-data/categories';

// Enhanced categories with premium styling and features
const webCategories = categories.map(cat => ({
  key: cat.key,
  label: cat.label,
  icon: getIconForCategory(cat.key),
  bgGradient: getGradientForCategory(cat.key),
  shadowColor: getShadowForCategory(cat.key),
  route: `/customer/suppliers-page?category=${cat.key}`,
  subtitle: getSubtitleForCategory(cat.key),
  badge: getBadgeForCategory(cat.key),
  badgeColor: getBadgeColorForCategory(cat.key),
  stats: getStatsForCategory(cat.key),
  special: cat.key === 'restaurants' // Mark restaurants as special/popular
}));

function getIconForCategory(key: string) {
  const icons: Record<string, any> = {
    'restaurants': Utensils,
    'clothings': Shirt,
    'pharmacies': Pill,
    'supermarkets': ShoppingCart
  };
  return icons[key] || Store;
}

function getGradientForCategory(key: string): string {
  const gradients: Record<string, string> = {
    'restaurants': 'from-orange-500 via-red-500 to-pink-600',
    'clothings': 'from-pink-500 via-purple-500 to-indigo-600',
    'pharmacies': 'from-green-500 via-emerald-500 to-teal-600',
    'supermarkets': 'from-blue-500 via-indigo-500 to-purple-600'
  };
  return gradients[key] || 'from-gray-500 via-gray-600 to-gray-700';
}

function getShadowForCategory(key: string): string {
  const shadows: Record<string, string> = {
    'restaurants': 'shadow-orange-500/25',
    'clothings': 'shadow-pink-500/25',
    'pharmacies': 'shadow-green-500/25',
    'supermarkets': 'shadow-blue-500/25'
  };
  return shadows[key] || 'shadow-gray-500/25';
}

function getSubtitleForCategory(key: string): string {
  const subtitles: Record<string, string> = {
    'restaurants': 'Delicious meals from local restaurants',
    'clothings': 'Fashion & style from top brands',
    'pharmacies': 'Health & wellness products',
    'supermarkets': 'Fresh groceries & daily essentials'
  };
  return subtitles[key] || 'Discover amazing products';
}

function getBadgeForCategory(key: string): string {
  const badges: Record<string, string> = {
    'restaurants': 'Popular',
    'clothings': 'Trending',
    'pharmacies': 'Essential',
    'supermarkets': 'Fresh'
  };
  return badges[key] || 'New';
}

function getBadgeColorForCategory(key: string): string {
  const badgeColors: Record<string, string> = {
    'restaurants': 'bg-yellow-400 text-yellow-900',
    'clothings': 'bg-pink-400 text-pink-900',
    'pharmacies': 'bg-green-400 text-green-900',
    'supermarkets': 'bg-blue-400 text-blue-900'
  };
  return badgeColors[key] || 'bg-gray-400 text-gray-900';
}

function getStatsForCategory(key: string): { suppliers: number; avgRating: number; deliveryTime: string } {
  const stats: Record<string, { suppliers: number; avgRating: number; deliveryTime: string }> = {
    'restaurants': { suppliers: 45, avgRating: 4.8, deliveryTime: '25-35 min' },
    'clothings': { suppliers: 28, avgRating: 4.6, deliveryTime: '1-2 days' },
    'pharmacies': { suppliers: 15, avgRating: 4.9, deliveryTime: '15-25 min' },
    'supermarkets': { suppliers: 32, avgRating: 4.7, deliveryTime: '30-45 min' }
  };
  return stats[key] || { suppliers: 20, avgRating: 4.5, deliveryTime: '30 min' };
}

const SupplierCategoriesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);

  // Handle scroll for header animation
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsHeaderCompact(currentScrollY > 100);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return webCategories;
    return webCategories.filter(category =>
      category.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [searchQuery]);

  const handleCategoryPress = (route: string) => {
    navigate(route);
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const handleBackPress = () => {
    navigate('/customer/home');
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-500/30 to-red-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-pink-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, -100, -20],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Sticky Header with Scroll Animation */}
        <motion.div
          className="fixed top-0 left-0 right-0 z-50 transition-all duration-500"
          animate={{
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3 }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
            >
              {/* Compact Header Content */}
              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  height: isHeaderCompact ? "auto" : 0,
                }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={handleBackPress}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                    >
                      <ArrowLeft className="text-white" size={20} />
                    </motion.button>
                    <div>
                      <h1 className="text-xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                        Categories
                      </h1>
                      <p className="text-white/60 text-xs">Choose your category</p>
                    </div>
                  </div>

                  {/* Compact Search */}
                  <div className="flex-1 max-w-md mx-8">
                    <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
                      <div className="flex items-center gap-3 p-3">
                        <Search size={18} className="text-white/60" />
                        <input
                          type="text"
                          placeholder="Search categories..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                        />
                        {searchQuery && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={clearSearch}
                            className="p-1 hover:bg-white/20 rounded-lg transition-all duration-200"
                          >
                            <X size={16} className="text-white/60" />
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative z-10 pt-32 pb-16"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Back Button for non-compact header */}
            <motion.div
              animate={{
                opacity: isHeaderCompact ? 0 : 1,
                height: isHeaderCompact ? 0 : "auto",
              }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden mb-8"
            >
              <motion.button
                whileHover={{ scale: 1.05, x: -5 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBackPress}
                className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-xl rounded-xl border border-white/20 text-white transition-all duration-200"
              >
                <ArrowLeft size={20} />
                <span>Back to Home</span>
              </motion.button>
            </motion.div>

            {/* Main Title */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mb-8"
            >
              <h1 className="text-5xl md:text-6xl font-bold text-white mb-4">
                Choose Your
                <span className="bg-gradient-to-r from-orange-400 via-pink-400 to-purple-400 bg-clip-text text-transparent block">
                  Category
                </span>
                <motion.span
                  animate={{ rotate: [0, 14, -8, 14, -4, 10, 0] }}
                  transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 3 }}
                  className="inline-block ml-4"
                >
                  🛍️
                </motion.span>
              </h1>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="flex items-center justify-center gap-3 text-white/80 text-lg"
              >
                <MapPin size={20} className="text-orange-400" />
                <span>Discover amazing suppliers in Nablus, Palestine</span>
              </motion.div>
            </motion.div>

            {/* Stats Row */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
              className="grid grid-cols-3 gap-8 mb-12"
            >
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">120+</div>
                <div className="text-white/70 text-lg">Suppliers</div>
              </motion.div>
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">4.8★</div>
                <div className="text-white/70 text-lg">Average Rating</div>
              </motion.div>
              <motion.div
                className="text-center"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-4xl font-bold text-white mb-2">Fast</div>
                <div className="text-white/70 text-lg">Delivery</div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>

        {/* Content Section */}
        <div className="px-4 sm:px-6 lg:px-8 pb-20">
          {/* Premium Search Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: isHeaderCompact ? 0 : 1,
              y: isHeaderCompact ? -20 : 0,
            }}
            transition={{ duration: 0.5 }}
            className="max-w-4xl mx-auto mb-16"
          >
            <div className="relative">
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl overflow-hidden">
                <div className="flex items-center gap-4 p-6">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-3 bg-gradient-to-r from-orange-500 to-pink-600 rounded-xl"
                  >
                    <Search size={24} className="text-white" />
                  </motion.div>
                  <input
                    type="text"
                    placeholder="Search categories, suppliers, or products…"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-lg font-medium"
                  />
                  {searchQuery.trim() && (
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={clearSearch}
                      className="p-2 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-200"
                    >
                      <X size={20} className="text-white" />
                    </motion.button>
                  )}
                </div>

                {/* Search suggestions */}
                {!searchQuery && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="px-6 pb-4"
                  >
                    <div className="flex flex-wrap gap-2">
                      {['Restaurants', 'Fashion', 'Pharmacy', 'Groceries'].map((suggestion, index) => (
                        <motion.button
                          key={suggestion}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * index }}
                          onClick={() => setSearchQuery(suggestion)}
                          className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full text-white/80 text-sm transition-all duration-200"
                        >
                          {suggestion}
                        </motion.button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Categories Grid */}
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="space-y-8"
            >
              <div className="text-center mb-8">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="text-4xl font-bold bg-gradient-to-r from-white via-orange-100 to-pink-100 bg-clip-text text-transparent mb-4"
                >
                  Premium Categories
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                  className="text-white/70 text-lg max-w-2xl mx-auto"
                >
                  Explore our curated selection of top-quality suppliers across different categories
                </motion.p>
              </div>

              {filteredCategories.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                  className="flex flex-col items-center justify-center py-20"
                >
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-6 bg-white/10 rounded-3xl mb-6"
                  >
                    <Search className="text-white/60" size={64} />
                  </motion.div>
                  <h3 className="text-white text-2xl font-bold mb-2">No categories found</h3>
                  <p className="text-white/60 text-center max-w-md">
                    Try searching for something else or browse our available categories
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={clearSearch}
                    className="mt-4 px-6 py-3 bg-gradient-to-r from-orange-500 to-pink-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-200"
                  >
                    Clear Search
                  </motion.button>
                </motion.div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {filteredCategories.map((category, index) => {
                    const Icon = category.icon;
                    return (
                      <motion.div
                        key={category.key}
                        initial={{ opacity: 0, scale: 0.8, y: 30 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        transition={{
                          duration: 0.8,
                          delay: index * 0.15,
                          type: "spring",
                          stiffness: 120
                        }}
                        whileHover={{
                          scale: 1.03,
                          y: -8,
                          transition: { duration: 0.3 }
                        }}
                        whileTap={{ scale: 0.97 }}
                        onClick={() => handleCategoryPress(category.route)}
                        className="group cursor-pointer"
                      >
                        {/* Premium Category Card */}
                        <div className={`relative rounded-3xl p-8 bg-gradient-to-br ${category.bgGradient} ${category.shadowColor} shadow-2xl hover:shadow-3xl transition-all duration-500 border border-white/10 overflow-hidden`}>
                          {/* Background Pattern */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
                          <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -translate-y-16 translate-x-16"></div>

                          {/* Badge */}
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.3 + index * 0.1 }}
                            className={`absolute top-4 right-4 px-3 py-1 ${category.badgeColor} rounded-full text-xs font-bold`}
                          >
                            {category.badge}
                          </motion.div>

                          {/* Special glow effect for popular category */}
                          {category.special && (
                            <motion.div
                              animate={{ opacity: [0.5, 1, 0.5] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="absolute inset-0 bg-gradient-to-r from-orange-400/20 via-pink-400/20 to-orange-400/20 rounded-3xl"
                            />
                          )}

                          <div className="relative z-10">
                            {/* Icon */}
                            <motion.div
                              whileHover={{ rotate: 360 }}
                              transition={{ duration: 0.6 }}
                              className="flex justify-center mb-6"
                            >
                              <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
                                <Icon
                                  className="group-hover:scale-110 transition-transform duration-300 text-white"
                                  size={48}
                                />
                              </div>
                            </motion.div>

                            {/* Content */}
                            <div className="text-center mb-6">
                              <h3 className="text-white font-bold text-2xl mb-2 leading-tight">
                                {category.label}
                              </h3>
                              <p className="text-white/80 text-sm mb-4 leading-relaxed">
                                {category.subtitle}
                              </p>

                              {/* Stats */}
                              <div className="grid grid-cols-3 gap-4 mb-4">
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg">{category.stats.suppliers}</div>
                                  <div className="text-white/60 text-xs">Suppliers</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg flex items-center justify-center gap-1">
                                    {category.stats.avgRating}
                                    <Star size={14} className="text-yellow-400 fill-current" />
                                  </div>
                                  <div className="text-white/60 text-xs">Rating</div>
                                </div>
                                <div className="text-center">
                                  <div className="text-white font-bold text-lg flex items-center justify-center gap-1">
                                    <Clock size={14} />
                                    {category.stats.deliveryTime.split(' ')[0]}
                                  </div>
                                  <div className="text-white/60 text-xs">Delivery</div>
                                </div>
                              </div>

                              {/* Action indicator */}
                              <motion.div
                                whileHover={{ x: 5 }}
                                className="flex items-center justify-center gap-2 text-white/60 text-sm"
                              >
                                <span>Explore Suppliers</span>
                                <ChevronRight size={16} />
                              </motion.div>
                            </div>

                            {/* Special effects for popular category */}
                            {category.special && (
                              <motion.div
                                animate={{ scale: [1, 1.05, 1] }}
                                transition={{ duration: 2, repeat: Infinity }}
                                className="flex items-center justify-center gap-2 text-white/90 text-xs bg-white/10 rounded-full py-2 px-4"
                              >
                                <Crown size={14} className="animate-pulse text-yellow-400" />
                                <span className="font-medium">Most Popular</span>
                                <Sparkles size={14} className="animate-pulse text-yellow-400" />
                              </motion.div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              )}
            </motion.div>
          </div>
        </div>

        {/* Premium Features Banner */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="fixed bottom-8 left-8 z-30 max-w-sm"
        >
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 border border-white/20 shadow-xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-orange-400 to-pink-500 rounded-xl">
                <Shield className="text-white" size={20} />
              </div>
              <div>
                <p className="text-white font-semibold text-sm">Trusted Platform</p>
                <p className="text-white/70 text-xs">Verified suppliers only</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Floating Action Button for Quick Access */}
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 1, type: "spring", stiffness: 200 }}
          className="fixed bottom-8 right-8 z-40"
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => navigate('/customer/home')}
            className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-600 rounded-full shadow-2xl flex items-center justify-center group"
          >
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full opacity-75 blur-lg"
            />
            <Store className="text-white relative z-10 group-hover:scale-110 transition-transform" size={28} />
          </motion.button>
        </motion.div>
      </div>
    </>
  );
};

export default SupplierCategoriesPage;
