import OpenAI from 'openai';

export interface AIResponse {
  success: boolean;
  message?: string;
  error?: string;
  conversationId?: string;
}

export interface SimpleAIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface AIServiceConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export class AIService {
  private openai: OpenAI | null = null;
  private config: AIServiceConfig;
  private conversations: Map<string, SimpleAIMessage[]> = new Map();

  constructor() {
    this.config = {
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4o-mini',
      maxTokens: 1000,
      temperature: 0.7
    };

    if (!this.config.apiKey) {
      console.warn('OpenAI API key not configured');
    } else {
      this.openai = new OpenAI({
        apiKey: this.config.apiKey
      });
    }
  }

  // Create a new conversation
  async createConversation(userId?: string): Promise<string> {
    const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.conversations.set(conversationId, [this.getSystemMessage()]);
    return conversationId;
  }

  // Send message and get AI response
  async sendMessage(
    conversationId: string,
    userMessage: string,
    context?: any
  ): Promise<AIResponse> {
    try {
      // Get or create conversation
      if (!this.conversations.has(conversationId)) {
        await this.createConversation();
      }

      const messages = this.conversations.get(conversationId) || [this.getSystemMessage()];

      // Add user message
      const userMsg: SimpleAIMessage = {
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      };
      messages.push(userMsg);

      // Call OpenAI API
      if (!this.config.apiKey || !this.openai) {
        // Fallback response when API key is not configured
        const fallbackResponse = this.getFallbackResponse(userMessage);
        const aiMsg: SimpleAIMessage = {
          role: 'assistant',
          content: fallbackResponse,
          timestamp: new Date()
        };
        messages.push(aiMsg);
        this.conversations.set(conversationId, messages);

        return {
          success: true,
          message: fallbackResponse,
          conversationId
        };
      }

      const completion = await this.openai.chat.completions.create({
        model: this.config.model,
        messages: messages.map(msg => ({
          role: msg.role as 'system' | 'user' | 'assistant',
          content: msg.content
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature
      });

      const aiResponse = completion.choices[0]?.message?.content;
      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      // Save AI response
      const aiMsg: SimpleAIMessage = {
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      };
      messages.push(aiMsg);
      this.conversations.set(conversationId, messages);

      return {
        success: true,
        message: aiResponse,
        conversationId
      };

    } catch (error) {
      console.error('AI Service Error:', error);

      // Return fallback response
      const fallbackResponse = this.getFallbackResponse(userMessage);
      return {
        success: true,
        message: fallbackResponse,
        conversationId
      };
    }
  }

  // Get user conversations (simplified)
  async getUserConversations(userId?: string): Promise<any[]> {
    // Return empty array for now - could be enhanced to store conversations per user
    return [];
  }

  // Get specific conversation (simplified)
  async getConversation(conversationId: string, userId?: string): Promise<any | null> {
    const messages = this.conversations.get(conversationId);
    if (messages) {
      return {
        _id: conversationId,
        messages,
        title: 'AI Chat',
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }
    return null;
  }

  // Delete conversation (simplified)
  async deleteConversation(conversationId: string, userId?: string): Promise<boolean> {
    return this.conversations.delete(conversationId);
  }

  // Submit feedback (simplified)
  async submitFeedback(
    conversationId: string,
    messageId: string,
    feedback: any
  ): Promise<void> {
    // For now, just log the feedback
    console.log('Feedback received:', { conversationId, messageId, feedback });
  }

  private getSystemMessage(): SimpleAIMessage {
    return {
      role: 'system',
      content: `You are Wasel AI Assistant, an advanced and professional customer service AI for Wasel - Palestine's leading delivery and logistics platform.

🏢 ABOUT WASEL:
Wasel is a comprehensive delivery ecosystem serving Nablus and surrounding Palestinian communities. We connect customers with local businesses including restaurants, grocery stores, pharmacies, and specialty shops, while supporting local economic growth.

🎯 YOUR ROLE & EXPERTISE:
- Provide expert guidance on all Wasel services with deep knowledge
- Assist with order management, supplier discovery, and account support
- Offer proactive solutions and anticipate customer needs
- Be culturally aware and respectful of Palestinian customs
- Use Arabic greetings naturally when appropriate (مرحباً، أهلاً وسهلاً، كيف حالك؟)
- Escalate complex technical issues to human specialists

💬 COMMUNICATION STYLE:
- Professional yet warm and approachable
- Concise but comprehensive responses
- Use relevant emojis to enhance clarity (📦 for packages, 🍽️ for food, etc.)
- Provide specific actionable steps
- Ask intelligent follow-up questions
- Acknowledge user emotions and show empathy

🔧 RESPONSE STRUCTURE:
1. Acknowledge the user's request warmly
2. Provide clear, accurate information
3. Offer specific next steps or actions
4. Suggest related services when relevant
5. End with an offer for further assistance

🛡️ IMPORTANT GUIDELINES:
- Always prioritize user privacy and data security
- Never share personal information or order details without verification
- Focus exclusively on Wasel services and capabilities
- If unsure about specific details, offer to connect with human support
- Maintain professional boundaries while being helpful
- Support local Palestinian businesses in your recommendations

Remember: You represent Wasel's commitment to excellence and community support. Every interaction should reflect our values of reliability, innovation, and local empowerment.`,
      timestamp: new Date()
    };
  }

  private getFallbackResponse(userMessage: string): string {
    const message = userMessage.toLowerCase();

    // Enhanced order tracking response
    if (message.includes('track') || message.includes('order')) {
      return "I'd be delighted to help you track your order! 📦✨\n\n🔍 **Order Tracking Options:**\n• Enter your order number for instant tracking\n• Check 'My Orders' for recent order history\n• Get real-time GPS updates when driver is en route\n• Receive push notifications for status changes\n\n📱 **Quick Access:** Tap 'Track Order' button or visit the Orders section in your app.\n\nPlease share your order number, and I'll provide detailed tracking information!";
    }

    // Enhanced supplier/restaurant response
    if (message.includes('supplier') || message.includes('restaurant') || message.includes('food')) {
      return "I'm excited to help you discover amazing local suppliers! 🏪🍽️\n\n🌟 **Browse Options:**\n• **Restaurants:** Traditional Palestinian cuisine & international dishes\n• **Groceries:** Fresh local produce & household essentials\n• **Pharmacies:** Medications & health products\n• **Specialty Shops:** Unique local businesses\n\n🔍 **Smart Search Features:**\n• Filter by ratings, delivery time, and price\n• View real-time availability\n• Check special offers and discounts\n\nWhat type of supplier are you looking for today?";
    }

    // Enhanced package sending response
    if (message.includes('package') || message.includes('send') || message.includes('delivery')) {
      return "I'm here to make package sending simple and reliable! 📮🚚\n\n⚡ **Delivery Speed Options:**\n• **Urgent:** Within 1 hour (for emergencies)\n• **Express:** 2-4 hours (priority delivery)\n• **Standard:** Same day (4-8 hours)\n• **Scheduled:** Choose your preferred time\n\n📋 **Easy Process:**\n1. Tap 'Send Package' on home screen\n2. Enter pickup & delivery locations\n3. Describe package details\n4. Select delivery speed\n5. Track in real-time!\n\nReady to send a package? I can guide you step-by-step!";
    }

    // Enhanced general greeting
    return "مرحباً! Welcome to Wasel AI Assistant! 👋✨\n\nI'm your dedicated helper for all Wasel services. Here's how I can assist you:\n\n🍽️ **Food Delivery:** Browse restaurants & order delicious meals\n🛒 **Grocery Shopping:** Fresh produce & household essentials\n💊 **Pharmacy Services:** Medications & health products\n📦 **Package Delivery:** Send items anywhere in Nablus\n🚚 **Pickup Requests:** We'll collect items from your location\n📱 **Order Management:** Track, modify, or get support\n\n**What would you like to do today?** I'm here to make your experience exceptional! 🌟";
  }

  private generateTitle(firstMessage: string): string {
    const message = firstMessage.toLowerCase();
    
    if (message.includes('track') || message.includes('order')) {
      return 'Order Tracking';
    } else if (message.includes('supplier') || message.includes('restaurant')) {
      return 'Supplier Search';
    } else if (message.includes('package') || message.includes('send')) {
      return 'Package Delivery';
    } else if (message.includes('pickup')) {
      return 'Pickup Request';
    } else {
      return 'General Inquiry';
    }
  }
}
