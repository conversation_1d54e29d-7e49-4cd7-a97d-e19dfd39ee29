import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, MapPin, Search, Navigation, Check } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Component to handle map clicks
function LocationMarker({ position, setPosition }: { 
  position: [number, number] | null; 
  setPosition: (pos: [number, number]) => void;
}) {
  useMapEvents({
    click(e) {
      setPosition([e.latlng.lat, e.latlng.lng]);
    },
  });

  return position === null ? null : (
    <Marker position={position}>
      <Popup>Selected location</Popup>
    </Marker>
  );
}

const SelectLocationPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type') || 'pickup'; // 'pickup' or 'delivery'
  
  const [position, setPosition] = useState<[number, number] | null>(null);
  const [address, setAddress] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Default to Nablus, Palestine
  const defaultCenter: [number, number] = [32.2211, 35.2544];

  // Mock addresses for search
  const mockAddresses = [
    { name: 'Nablus City Center', address: 'Nablus, Palestine', lat: 32.2211, lng: 35.2544 },
    { name: 'An-Najah University', address: 'An-Najah University, Nablus', lat: 32.2319, lng: 35.2542 },
    { name: 'Rafidia Hospital', address: 'Rafidia Hospital, Nablus', lat: 32.2156, lng: 35.2631 },
    { name: 'Old City Nablus', address: 'Old City, Nablus, Palestine', lat: 32.2208, lng: 35.2625 },
    { name: 'Ramallah City Center', address: 'Ramallah, Palestine', lat: 31.9038, lng: 35.2034 },
    { name: 'Jerusalem', address: 'Jerusalem, Palestine', lat: 31.7683, lng: 35.2137 },
    { name: 'Bethlehem', address: 'Bethlehem, Palestine', lat: 31.7054, lng: 35.2024 }
  ];

  const filteredAddresses = mockAddresses.filter(addr =>
    addr.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    addr.address.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddressSelect = (addr: typeof mockAddresses[0]) => {
    setPosition([addr.lat, addr.lng]);
    setAddress(addr.address);
    setSearchQuery('');
  };

  const getCurrentLocation = () => {
    setIsLoading(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setPosition([latitude, longitude]);
          setAddress(`${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
          setIsLoading(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          setIsLoading(false);
          alert('Unable to get your current location. Please select manually.');
        }
      );
    } else {
      setIsLoading(false);
      alert('Geolocation is not supported by this browser.');
    }
  };

  const handleConfirm = () => {
    if (!position || !address) {
      alert('Please select a location');
      return;
    }

    const locationData = {
      address,
      lat: position[0],
      lng: position[1],
      type
    };

    // Navigate back with location data
    navigate('..', { state: { selectedLocation: locationData } });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">
              Select {type === 'pickup' ? 'Pickup' : 'Delivery'} Location
            </h1>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Search Panel */}
          <div className="lg:col-span-1 space-y-6">
            {/* Search */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Search Location</h2>
              
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search for a location..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                <button
                  onClick={getCurrentLocation}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center gap-2 py-3 px-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
                >
                  <Navigation className="w-5 h-5" />
                  {isLoading ? 'Getting Location...' : 'Use Current Location'}
                </button>
              </div>

              {/* Search Results */}
              {searchQuery && (
                <div className="mt-4 space-y-2">
                  {filteredAddresses.map((addr, index) => (
                    <button
                      key={index}
                      onClick={() => handleAddressSelect(addr)}
                      className="w-full text-left p-3 hover:bg-gray-50 rounded-lg border border-gray-200 transition-colors"
                    >
                      <div className="font-medium text-gray-900">{addr.name}</div>
                      <div className="text-sm text-gray-600">{addr.address}</div>
                    </button>
                  ))}
                  
                  {filteredAddresses.length === 0 && (
                    <div className="text-center py-4 text-gray-500">
                      No locations found
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Selected Location */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Selected Location</h2>
              
              {position ? (
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-purple-600 mt-1" />
                    <div>
                      <p className="font-medium text-gray-900">Address</p>
                      <p className="text-gray-600">{address}</p>
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    Coordinates: {position[0].toFixed(6)}, {position[1].toFixed(6)}
                  </div>
                  
                  <button
                    onClick={handleConfirm}
                    className="w-full flex items-center justify-center gap-2 py-3 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Check className="w-5 h-5" />
                    Confirm Location
                  </button>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <MapPin className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>Click on the map or search to select a location</p>
                </div>
              )}
            </div>
          </div>

          {/* Map */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="h-96 lg:h-[600px]">
                <MapContainer
                  center={position || defaultCenter}
                  zoom={13}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  <LocationMarker position={position} setPosition={setPosition} />
                </MapContainer>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-semibold text-blue-900 mb-2">How to select a location:</h3>
          <ul className="text-blue-800 space-y-1">
            <li>• Click anywhere on the map to place a marker</li>
            <li>• Use the search box to find specific locations</li>
            <li>• Click "Use Current Location" to automatically detect your position</li>
            <li>• Confirm your selection when you're satisfied with the location</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SelectLocationPage;
