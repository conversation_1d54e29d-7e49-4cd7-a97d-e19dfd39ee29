import React, { useEffect } from 'react';
import { useCurrentUserData } from '../useCurrentUserData';

/**
 * Component that initializes user authentication state on app start
 * This should be placed at the root of the app to ensure user state is loaded
 */
export const AuthInitializer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { initializeUser } = useCurrentUserData();

  useEffect(() => {
    console.log('🚀 AuthInitializer: Starting user initialization...');
    const init = async () => {
      try {
        await initializeUser();
        console.log('✅ AuthInitializer: User initialization completed');
      } catch (error) {
        console.error('❌ AuthInitializer: User initialization failed:', error);
      }
    };
    init();
  }, []); // Empty dependency array to run only once on mount

  return <>{children}</>;
};
