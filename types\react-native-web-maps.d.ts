declare module 'react-native-web-maps' {
  import { Component } from 'react';

  export interface LatLng {
    latitude: number;
    longitude: number;
  }

  export interface Region extends LatLng {
    latitudeDelta: number;
    longitudeDelta: number;
  }

  export interface MapPressEvent {
    nativeEvent: {
      coordinate: LatLng;
    };
  }

  export interface MapViewProps {
    style?: any;
    region?: Region;
    initialRegion?: Region;
    onRegionChange?: (region: Region) => void;
    onRegionChangeComplete?: (region: Region) => void;
    onPress?: (event: MapPressEvent) => void;
    showsUserLocation?: boolean;
    followsUserLocation?: boolean;
    showsMyLocationButton?: boolean;
    showsPointsOfInterest?: boolean;
    showsCompass?: boolean;
    showsScale?: boolean;
    showsBuildings?: boolean;
    showsTraffic?: boolean;
    showsIndoors?: boolean;
    showsIndoorLevelPicker?: boolean;
    zoomEnabled?: boolean;
    scrollEnabled?: boolean;
    pitchEnabled?: boolean;
    rotateEnabled?: boolean;
    mapType?: 'standard' | 'satellite' | 'hybrid' | 'terrain';
    minZoomLevel?: number;
    maxZoomLevel?: number;
    children?: React.ReactNode;
  }

  export interface MarkerProps {
    coordinate: LatLng;
    title?: string;
    description?: string;
    image?: any;
    pinColor?: string;
    anchor?: { x: number; y: number };
    calloutAnchor?: { x: number; y: number };
    flat?: boolean;
    identifier?: string;
    rotation?: number;
    draggable?: boolean;
    onPress?: () => void;
    onSelect?: () => void;
    onDeselect?: () => void;
    onCalloutPress?: () => void;
    onDragStart?: () => void;
    onDrag?: () => void;
    onDragEnd?: () => void;
    children?: React.ReactNode;
  }

  export interface PolylineProps {
    coordinates: LatLng[];
    strokeWidth?: number;
    strokeColor?: string;
    fillColor?: string;
    zIndex?: number;
    lineCap?: 'butt' | 'round' | 'square';
    lineJoin?: 'miter' | 'round' | 'bevel';
    miterLimit?: number;
    geodesic?: boolean;
    lineDashPhase?: number;
    lineDashPattern?: number[];
  }

  export interface PolygonProps {
    coordinates: LatLng[];
    holes?: LatLng[][];
    strokeWidth?: number;
    strokeColor?: string;
    fillColor?: string;
    zIndex?: number;
    geodesic?: boolean;
    lineCap?: 'butt' | 'round' | 'square';
    lineJoin?: 'miter' | 'round' | 'bevel';
    miterLimit?: number;
    lineDashPhase?: number;
    lineDashPattern?: number[];
  }

  export interface CircleProps {
    center: LatLng;
    radius: number;
    strokeWidth?: number;
    strokeColor?: string;
    fillColor?: string;
    zIndex?: number;
  }

  export class MapView extends Component<MapViewProps> {}
  export class Marker extends Component<MarkerProps> {}
  export class Polyline extends Component<PolylineProps> {}
  export class Polygon extends Component<PolygonProps> {}
  export class Circle extends Component<CircleProps> {}

  export default MapView;
}
