import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User, Mail, Lock, Phone, Calendar, MapPin,
  Building, Clock, Eye, EyeOff, ChevronLeft,
  ChevronRight, AlertCircle, Check
} from 'lucide-react';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import { apiService, type SignupRequest } from '../../services/api';

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  username: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  city: string;
  country: string;
  role: 'customer' | 'supplier';
  storeName?: string;
  businessType?: string;
  openHours?: string;
  location?: [number, number]; // [longitude, latitude]
}

const SignupPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);

  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    trigger,
  } = useForm<SignupFormData>({
    defaultValues: {
      role: 'customer',
      country: 'Jordan',
    },
  });

  const watchedRole = watch('role');
  const watchedPassword = watch('password');

  // Location functions
  const requestLocation = async () => {
    setLocationLoading(true);
    setError('');

    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by this browser');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        });
      });

      const location: [number, number] = [
        position.coords.longitude,
        position.coords.latitude
      ];

      setUserLocation(location);
    } catch (error) {
      console.error('Location error:', error);
      setError('Unable to get your location. You can skip this step or try again.');
    } finally {
      setLocationLoading(false);
    }
  };

  const skipLocation = () => {
    setUserLocation(null);
  };

  const totalSteps = watchedRole === 'supplier' ? 5 : 4;

  const nextStep = async () => {
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);

    if (isValid) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
      setError('');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
    setError('');
  };

  const getFieldsForStep = (step: number): (keyof SignupFormData)[] => {
    switch (step) {
      case 1:
        return ['firstName', 'lastName', 'email', 'phoneNumber'];
      case 2:
        return ['password', 'confirmPassword', 'username'];
      case 3:
        return ['dateOfBirth', 'gender', 'address', 'city', 'country', 'role'];
      case 4:
        return watchedRole === 'supplier' ? ['storeName', 'businessType', 'openHours'] : [];
      case 5:
        return []; // Location step - no required fields
      default:
        return [];
    }
  };

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const signupData: SignupRequest = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phoneNumber: data.phoneNumber,
        password: data.password,
        username: data.username,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        address: data.address,
        city: data.city,
        country: data.country,
        role: data.role,
        storeName: data.storeName,
        businessType: data.businessType,
        openHours: data.openHours,
        location: userLocation || undefined,
        notifications: true,
      };

      const response = await apiService.signup(signupData);

      if (response.success) {
        navigate('/auth/email-verification', {
          state: { email: data.email }
        });
      } else {
        setError(response.message || 'Signup failed. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="First Name"
                placeholder="John"
                icon={<User size={18} />}
                error={errors.firstName?.message}
                {...register('firstName', { required: 'First name is required' })}
              />
              <Input
                label="Last Name"
                placeholder="Doe"
                icon={<User size={18} />}
                error={errors.lastName?.message}
                {...register('lastName', { required: 'Last name is required' })}
              />
            </div>

            <Input
              label="Email"
              type="email"
              placeholder="<EMAIL>"
              icon={<Mail size={18} />}
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
            />

            <Input
              label="Phone Number"
              type="tel"
              placeholder="+962 7X XXX XXXX"
              icon={<Phone size={18} />}
              error={errors.phoneNumber?.message}
              {...register('phoneNumber', { required: 'Phone number is required' })}
            />
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <Input
              label="Username"
              placeholder="johndoe"
              icon={<User size={18} />}
              error={errors.username?.message}
              {...register('username', { required: 'Username is required' })}
            />

            <div className="relative">
              <Input
                label="Password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter password"
                icon={<Lock size={18} />}
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
              />
              {/* <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button> */}
            </div>

            <div className="relative">
              <Input
                label="Confirm Password"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm password"
                icon={<Lock size={18} />}
                error={errors.confirmPassword?.message}
                {...register('confirmPassword', {
                  required: 'Please confirm your password',
                  validate: value =>
                    value === watchedPassword || 'Passwords do not match',
                })}
              />
              {/* <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
              >
                {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button> */}
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <Input
              label="Date of Birth"
              type="date"
              icon={<Calendar size={18} />}
              error={errors.dateOfBirth?.message}
              {...register('dateOfBirth', { required: 'Date of birth is required' })}
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Gender
              </label>
              <select
                className="input-field"
                {...register('gender', { required: 'Gender is required' })}
              >
                <option value="">Select gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
              {errors.gender && (
                <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
              )}
            </div>

            <Input
              label="Address"
              placeholder="Street address"
              icon={<MapPin size={18} />}
              error={errors.address?.message}
              {...register('address', { required: 'Address is required' })}
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="City"
                placeholder="Amman"
                icon={<MapPin size={18} />}
                error={errors.city?.message}
                {...register('city', { required: 'City is required' })}
              />
              <Input
                label="Country"
                placeholder="Jordan"
                icon={<MapPin size={18} />}
                error={errors.country?.message}
                {...register('country', { required: 'Country is required' })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Type
              </label>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    value="customer"
                    {...register('role', { required: 'Please select account type' })}
                    className="mr-3"
                  />
                  <div>
                    <div className="font-medium">Customer</div>
                    <div className="text-sm text-gray-600">Order and receive deliveries</div>
                  </div>
                </label>
                <label className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    value="supplier"
                    {...register('role', { required: 'Please select account type' })}
                    className="mr-3"
                  />
                  <div>
                    <div className="font-medium">Supplier</div>
                    <div className="text-sm text-gray-600">Provide products and services</div>
                  </div>
                </label>
              </div>
              {errors.role && (
                <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
              )}
            </div>
          </motion.div>
        );

      case 4:
        return watchedRole === 'supplier' ? (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-4"
          >
            <Input
              label="Store Name"
              placeholder="Your business name"
              icon={<Building size={18} />}
              error={errors.storeName?.message}
              {...register('storeName', { required: 'Store name is required for suppliers' })}
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Business Type
              </label>
              <select
                className="input-field"
                {...register('businessType', { required: 'Business type is required for suppliers' })}
              >
                <option value="">Select business type</option>
                <option value="restaurant">Restaurant</option>
                <option value="grocery">Grocery Store</option>
                <option value="pharmacy">Pharmacy</option>
                <option value="electronics">Electronics</option>
                <option value="clothing">Clothing</option>
                <option value="other">Other</option>
              </select>
              {errors.businessType && (
                <p className="mt-1 text-sm text-red-600">{errors.businessType.message}</p>
              )}
            </div>

            <Input
              label="Opening Hours"
              placeholder="e.g., 9:00 AM - 10:00 PM"
              icon={<Clock size={18} />}
              error={errors.openHours?.message}
              {...register('openHours', { required: 'Opening hours are required for suppliers' })}
            />
          </motion.div>
        ) : (
          // Location step for customers
          <motion.div
            key="step4-location"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6 text-center"
          >
            <div className="space-y-4">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
                <MapPin size={32} className="text-primary-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Enable Location Services
                </h3>
                <p className="text-gray-600">
                  Help us provide better delivery services by sharing your location. This is optional but recommended.
                </p>
              </div>
            </div>

            {userLocation ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-center space-x-2 text-green-700">
                  <Check size={20} />
                  <span className="font-medium">Location detected successfully!</span>
                </div>
                <p className="text-green-600 text-sm mt-1">
                  Coordinates: {userLocation[1].toFixed(4)}, {userLocation[0].toFixed(4)}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <Button
                  type="button"
                  variant="primary"
                  onClick={requestLocation}
                  loading={locationLoading}
                  disabled={locationLoading}
                  className="w-full flex items-center justify-center space-x-2"
                >
                  <MapPin size={18} />
                  <span>{locationLoading ? 'Getting Location...' : 'Allow Location Access'}</span>
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={skipLocation}
                  className="w-full"
                >
                  Skip for Now
                </Button>
              </div>
            )}
          </motion.div>
        );

      case 5:
        return watchedRole === 'supplier' ? (
          // Location step for suppliers
          <motion.div
            key="step5-location"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6 text-center"
          >
            <div className="space-y-4">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
                <MapPin size={32} className="text-primary-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Set Your Store Location
                </h3>
                <p className="text-gray-600">
                  Help customers find your store by sharing your business location. This is optional but recommended.
                </p>
              </div>
            </div>

            {userLocation ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-center space-x-2 text-green-700">
                  <Check size={20} />
                  <span className="font-medium">Store location set successfully!</span>
                </div>
                <p className="text-green-600 text-sm mt-1">
                  Coordinates: {userLocation[1].toFixed(4)}, {userLocation[0].toFixed(4)}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                <Button
                  type="button"
                  variant="primary"
                  onClick={requestLocation}
                  loading={locationLoading}
                  disabled={locationLoading}
                  className="w-full flex items-center justify-center space-x-2"
                >
                  <MapPin size={18} />
                  <span>{locationLoading ? 'Getting Location...' : 'Set Store Location'}</span>
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={skipLocation}
                  className="w-full"
                >
                  Skip for Now
                </Button>
              </div>
            )}
          </motion.div>
        ) : null;

      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="text-2xl font-bold text-gray-900 mb-2 text-center">
        Create Account
      </h2>
      <p className="text-gray-600 text-center mb-6">
        Step {currentStep} of {totalSteps}
      </p>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between mb-2">
          {Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                i + 1 <= currentStep
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {i + 1 < currentStep ? <Check size={16} /> : i + 1}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          />
        </div>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 text-red-700"
        >
          <AlertCircle size={16} />
          <span className="text-sm">{error}</span>
        </motion.div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <AnimatePresence mode="wait">
          {renderStep()}
        </AnimatePresence>

        <div className="flex justify-between mt-6">
          {currentStep > 1 && (
            <Button
              type="button"
              variant="secondary"
              onClick={prevStep}
              className="flex items-center space-x-2"
            >
              <ChevronLeft size={16} />
              <span>Previous</span>
            </Button>
          )}

          <div className="ml-auto">
            {currentStep < totalSteps ? (
              <Button
                type="button"
                variant="primary"
                onClick={nextStep}
                className="flex items-center space-x-2"
              >
                <span>Next</span>
                <ChevronRight size={16} />
              </Button>
            ) : (
              <Button
                type="submit"
                variant="primary"
                loading={isLoading}
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                <span>Create Account</span>
              </Button>
            )}
          </div>
        </div>
      </form>

      <div className="mt-6 text-center">
        <p className="text-gray-600">
          Already have an account?{' '}
          <Link
            to="/auth/login"
            className="text-primary-600 hover:text-primary-700 font-medium transition-colors"
          >
            Sign in
          </Link>
        </p>
      </div>
    </motion.div>
  );
};

export default SignupPage;
