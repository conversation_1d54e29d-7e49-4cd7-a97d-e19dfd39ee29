{"expo": {"name": "wasel", "slug": "wasel", "version": "1.0.0", "owner": "delivery-project", "scheme": "wasel", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-font", ["expo-location", {"locationWhenInUsePermission": "Show current location on map."}], "expo-image-picker", "@react-native-community/datetimepicker", "expo-localization"], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "package": "com.omarj2004.wasel", "config": {"googleMaps": {"apiKey": "process.env.GOOGLE_MAPS_API_KEY"}}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECORD_AUDIO"]}, "extra": {"router": {}, "eas": {"projectId": "282556d0-7baa-43a2-94e5-28c6c4239267"}}}}