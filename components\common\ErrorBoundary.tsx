import React from 'react';
import { YStack, Text, Button } from 'tamagui';
import { Alert } from 'react-native';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      return (
        <YStack flex={1} alignItems="center" justifyContent="center" padding="$4" gap="$4">
          <Text fontSize="$6" fontWeight="600" color="$red10" textAlign="center">
            Something went wrong
          </Text>
          <Text fontSize="$4" color="$gray10" textAlign="center">
            {this.state.error?.message || 'An unexpected error occurred'}
          </Text>
          <Button onPress={this.resetError} backgroundColor="$blue10">
            <Text color="white">Try Again</Text>
          </Button>
        </YStack>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: string) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo);
    Alert.alert(
      'Error',
      error.message || 'An unexpected error occurred',
      [{ text: 'OK' }]
    );
  };

  return handleError;
};
