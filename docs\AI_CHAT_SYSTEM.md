# Wasel AI Chat System

A comprehensive AI-powered customer assistance system for the Wasel delivery platform, providing intelligent support for order management, supplier discovery, and customer service.

## 🌟 Features

### Core Functionality
- **Intelligent Chat Interface**: Professional chat UI with message bubbles, typing indicators, and status tracking
- **AI-Powered Responses**: OpenAI GPT-4 integration with fallback responses for offline scenarios
- **Contextual Assistance**: Personalized responses based on user data, location, and order history
- **Smart Quick Actions**: Contextual action buttons that navigate to relevant app sections
- **Real-time Status**: Message delivery status, typing indicators, and connection status
- **Offline Support**: Graceful degradation with cached responses when offline

### Advanced Features
- **Knowledge Base Integration**: Comprehensive Wasel service information for accurate responses
- **User Context Awareness**: Integration with user orders, preferences, and location data
- **Conversation Management**: Persistent chat history with conversation storage
- **Error Handling**: Robust error handling with retry mechanisms
- **Performance Optimization**: Efficient message handling and memory management

## 🏗️ Architecture

### Frontend Components
```
components/customer-pages-components/
├── AIChatGUI.tsx           # Main chat interface component
└── CustomerScreenContent.tsx # Integration with existing layout

services/
├── aiService.ts            # AI service integration
├── aiKnowledgeBase.ts      # Wasel knowledge base
└── userContextService.ts   # User context management
```

### Backend Services
```
backend/src/
├── routes/aiChatRoutes.ts  # API endpoints for chat functionality
├── services/aiService.ts   # Backend AI service
├── models/
│   ├── Conversation.ts     # Conversation data model
│   ├── Message.ts          # Message data model
│   └── Feedback.ts         # Feedback data model
```

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- React Native development environment
- OpenAI API key (optional, fallback responses available)
- MongoDB database (for backend)

### Installation

1. **Install Dependencies**
   ```bash
   npm install openai @react-native-async-storage/async-storage
   ```

2. **Environment Setup**
   ```bash
   # Add to your .env file
   EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
   ```

3. **Database Setup**
   ```bash
   # MongoDB collections will be created automatically
   # Ensure MongoDB connection is configured in your backend
   ```

### Configuration

1. **AI Service Configuration**
   ```typescript
   // services/aiService.ts
   const AI_CONFIG = {
     MODEL: 'gpt-4o-mini',
     MAX_TOKENS: 1000,
     TEMPERATURE: 0.7,
   };
   ```

2. **Knowledge Base Customization**
   ```typescript
   // services/aiKnowledgeBase.ts
   // Update waselKnowledgeBase with your specific service information
   ```

## 📱 Usage

### Basic Integration

1. **Add AI Chat Tab to Navigation**
   ```typescript
   // app/(customer-pages)/_layout.tsx
   <Tabs.Screen
     name="ai-chat"
     options={{
       title: 'AI Assistant',
       tabBarIcon: ({ color, size, focused }) => (
         <Ionicons name={focused ? "chatbubble-ellipses" : "chatbubble-ellipses-outline"} 
                   size={size} color={color} />
       ),
     }}
   />
   ```

2. **Create Chat Route**
   ```typescript
   // app/(customer-pages)/ai-chat.tsx
   export default function AIChat() {
     return (
       <Container>
         <CustomerScreenContent title="AIChat" />
       </Container>
     );
   }
   ```

### Advanced Usage

1. **Custom Context Integration**
   ```typescript
   // Initialize with user-specific context
   await userContextService.initializeUserContext(userId);
   
   // Update context based on user actions
   userContextService.updateLocation(newLocation);
   userContextService.addRecentOrder(orderData);
   ```

2. **Custom Knowledge Base**
   ```typescript
   // Add custom knowledge topics
   const customKnowledge: KnowledgeCategory = {
     category: "Custom Services",
     topics: [
       {
         topic: "Special Delivery",
         content: "Information about special delivery services...",
         keywords: ["special", "express", "urgent"],
         examples: ["I need urgent delivery", "Special delivery options"]
       }
     ]
   };
   ```

## 🔧 API Reference

### Frontend Services

#### AIService
```typescript
// Create conversation
const conversationId = aiService.createConversation();

// Send message
const response = await aiService.sendMessage(conversationId, message, context);

// Get conversation
const conversation = aiService.getConversation(conversationId);
```

#### UserContextService
```typescript
// Initialize context
await userContextService.initializeUserContext(userId);

// Get context summary
const summary = userContextService.getContextSummary();

// Get smart suggestions
const suggestions = userContextService.getSmartSuggestions();
```

### Backend API Endpoints

#### Chat Messages
```http
POST /api/ai-chat/message
Content-Type: application/json
Authorization: Bearer <token>

{
  "message": "How can I track my order?",
  "conversationId": "optional_conversation_id",
  "context": {}
}
```

#### Conversation Management
```http
GET /api/ai-chat/conversations
GET /api/ai-chat/conversation/:id
DELETE /api/ai-chat/conversation/:id
```

#### Feedback
```http
POST /api/ai-chat/feedback
{
  "conversationId": "conv_id",
  "messageId": "msg_id",
  "rating": 5,
  "feedback": "Very helpful!"
}
```

## 🧪 Testing

### Running Tests
```bash
# Run all AI chat system tests
npm test __tests__/aiChatSystem.test.ts

# Run with coverage
npm test -- --coverage
```

### Test Scenarios
- Basic chat functionality
- Error handling and fallback responses
- User context integration
- Knowledge base queries
- Performance with multiple conversations
- Edge cases (empty messages, special characters, Arabic text)

## 🎨 Customization

### UI Customization
```typescript
// Customize message bubble colors
backgroundColor={message.isUser ? '$purple10' : '$gray2'}

// Customize typing indicator
{isTyping && <Text>AI is thinking...</Text>}

// Custom quick actions
const customQuickActions = [
  { id: '1', label: 'Custom Action', icon: 'custom-icon', action: 'custom_action' }
];
```

### AI Response Customization
```typescript
// Custom system prompt
const customSystemPrompt = `
You are a specialized assistant for [Your Service].
Focus on [Your Specific Domain].
Always be [Your Brand Voice].
`;
```

## 📊 Analytics & Monitoring

### Conversation Analytics
- Message volume and response times
- User satisfaction ratings
- Common query patterns
- Error rates and fallback usage

### Performance Metrics
- Response latency
- API usage and costs
- Conversation completion rates
- User engagement metrics

## 🔒 Security & Privacy

### Data Protection
- All conversations encrypted in transit and at rest
- User data anonymization options
- GDPR compliance features
- Secure API key management

### Rate Limiting
- Per-user message limits
- API rate limiting
- Abuse detection and prevention

## 🚀 Deployment

### Production Checklist
- [ ] OpenAI API key configured
- [ ] Database connections tested
- [ ] Error monitoring setup
- [ ] Rate limiting configured
- [ ] Analytics tracking enabled
- [ ] Backup and recovery tested

### Environment Variables
```bash
OPENAI_API_KEY=your_production_key
MONGODB_URI=your_mongodb_connection
AI_CHAT_RATE_LIMIT=100
AI_CHAT_MAX_CONVERSATIONS=50
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This AI Chat System is part of the Wasel platform and follows the project's licensing terms.

## 🆘 Support

For technical support or questions:
- Check the troubleshooting guide
- Review test cases for usage examples
- Contact the development team

---

**Built with ❤️ for the Wasel community**
