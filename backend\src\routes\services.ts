import { Router } from 'express';
import { body } from 'express-validator';
import { ServiceController } from '../controllers/serviceController';
import { authenticate } from '../middleware/auth';

const router = Router();

// Validation rules for service creation/update
const serviceValidation = [
  body('key')
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Key must be a string between 1 and 50 characters'),
  body('label')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Label must be a string between 1 and 100 characters'),
  body('icon')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Icon must be a string between 1 and 100 characters'),
  body('color')
    .isString()
    .trim()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('Color must be a valid hex color code'),
  body('route')
    .isString()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Route must be a string between 1 and 200 characters'),
  body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must be a string with maximum 500 characters'),
  body('order')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Order must be a non-negative integer')
];

// Public routes
router.get('/', ServiceController.getServices);
router.get('/:key', ServiceController.getServiceByKey);

// Protected routes (admin only)
router.post('/', authenticate, serviceValidation, ServiceController.createService);
router.put('/:key', authenticate, serviceValidation, ServiceController.updateService);
router.delete('/:key', authenticate, ServiceController.deleteService);
router.patch('/:key/toggle-status', authenticate, ServiceController.toggleServiceStatus);

export default router;
