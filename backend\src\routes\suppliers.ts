import { Router } from 'express';
import { query } from 'express-validator';
import { SupplierController } from '../controllers/supplierController';

const router = Router();

// Validation rules for supplier queries
const supplierQueryValidation = [
  query('category')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be a string between 1 and 50 characters'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search must be a string between 1 and 100 characters'),
  query('lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be a valid number between -90 and 90'),
  query('lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be a valid number between -180 and 180'),
  query('radius')
    .optional()
    .isFloat({ min: 0.1, max: 100 })
    .withMessage('Radius must be a number between 0.1 and 100 km'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be an integer between 1 and 100'),
  query('sortBy')
    .optional()
    .isIn(['name', 'rating', 'deliveryTime', 'createdAt'])
    .withMessage('SortBy must be one of: name, rating, deliveryTime, createdAt'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('SortOrder must be either asc or desc')
];

const productQueryValidation = [
  query('category')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be a string between 1 and 50 characters'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search must be a string between 1 and 100 characters'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be an integer between 1 and 100')
];

// Public routes
router.get('/', supplierQueryValidation, SupplierController.getSuppliers);
router.get('/category/:category', supplierQueryValidation, SupplierController.getSuppliersByCategory);
router.get('/:id', SupplierController.getSupplierById);
router.get('/:id/products', productQueryValidation, SupplierController.getSupplierProducts);
router.get('/:supplierId/products/:productId', SupplierController.getProductById);

export default router;
