import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

// Import translation files
import en from './translations/en.json';
import ar from './translations/ar.json';

const resources = {
  en: {
    translation: en,
  },
  ar: {
    translation: ar,
  },
};

// Get device language with fallback
const getDeviceLanguage = () => {
  try {
    const locale = Localization.locale;
    if (!locale || typeof locale !== 'string') {
      return 'en'; // Default fallback
    }
    const deviceLanguage = locale.split('-')[0];
    return deviceLanguage === 'ar' ? 'ar' : 'en';
  } catch (error) {
    console.warn('Error getting device language:', error);
    return 'en'; // Default fallback
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: getDeviceLanguage(), // Default language
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false, // React already does escaping
    },
    
    react: {
      useSuspense: false, // Disable suspense for React Native
    },
    
    // Enable debugging in development
    debug: __DEV__,
  });

export default i18n;
