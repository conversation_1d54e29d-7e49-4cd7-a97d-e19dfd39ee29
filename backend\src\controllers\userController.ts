import { Response } from 'express';
import User from '../models/User';
import { EmailService } from '../services/emailService';
import { 
  AuthenticatedRequest, 
  UpdateProfileRequest, 
  ChangePasswordRequest,
  ApiResponse 
} from '../types';

export class UserController {
  static async getProfile(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Profile retrieved successfully',
        data: { user: user.toJSON() },
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async updateProfile(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;
      const updates: UpdateProfileRequest = req.body;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Update allowed fields - expanded to include all profile fields
      const allowedUpdates = [
        'firstName',
        'lastName',
        'phoneNumber',
        'address',
        'city',
        'country',
        'dateOfBirth',
        'gender'
      ];
      const updateData: any = {};

      allowedUpdates.forEach(field => {
        if (updates[field as keyof UpdateProfileRequest] !== undefined) {
          updateData[field] = updates[field as keyof UpdateProfileRequest];
        }
      });

      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedUser) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser.toJSON() },
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async changePassword(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;
      const { currentPassword, newPassword }: ChangePasswordRequest = req.body;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Get user with password for comparison
      const userWithPassword = await User.findById(user._id).select('+password');
      if (!userWithPassword) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      // Verify current password
      const isCurrentPasswordValid = await userWithPassword.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        res.status(400).json({
          success: false,
          message: 'Current password is incorrect',
        });
        return;
      }

      // Update password
      userWithPassword.password = newPassword;
      userWithPassword.refreshTokens = []; // Logout from all devices
      await userWithPassword.save();

      // Send notification email
      try {
        await EmailService.sendPasswordChangeNotification(userWithPassword);
      } catch (emailError) {
        console.error('Failed to send password change notification:', emailError);
        // Don't fail the request if email fails
      }

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async deleteAccount(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const user = req.user;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Soft delete - deactivate account
      await User.findByIdAndUpdate(user._id, { 
        isActive: false,
        refreshTokens: [] // Logout from all devices
      });

      res.status(200).json({
        success: true,
        message: 'Account deactivated successfully',
      });
    } catch (error) {
      console.error('Delete account error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  static async getUserById(req: AuthenticatedRequest, res: Response<ApiResponse>): Promise<void> {
    try {
      const { userId } = req.params;
      const currentUser = req.user;

      if (!currentUser) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      // Only allow users to view their own profile or admins to view any profile
      if (currentUser._id !== userId && currentUser.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: 'Access denied',
        });
        return;
      }

      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found',
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: 'User retrieved successfully',
        data: { user: user.toJSON() },
      });
    } catch (error) {
      console.error('Get user by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}
