import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  MapPin,
  User,
  Phone,
  Clock,
  Eye,
  Search,
  X,
  Navigation,
  Truck,
  CheckCircle,
  RotateCcw,
  Calendar,
  Hash
} from 'lucide-react';
import { getUserPackages } from '../../../../services/apiService';

// Mock data matching mobile structure
const mockSentPackages = [
  {
    id: 'PKG_1752326131054_abc123',
    createdAt: '2025-01-12T10:30:00Z',
    pickup: {
      address: 'Askar Camp - Mosque immigrants, Nablus',
      lat: 32.2211,
      lng: 35.2544
    },
    dropoff: {
      address: 'An-Najah National University, Nablus',
      lat: 32.2278,
      lng: 35.2542
    },
    receiverName: '<PERSON>',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    status: 'Delivered' as const,
    estimatedTime: '30-45 mins',
    driverName: '<PERSON>',
    driverPhone: '0597654321',
    cost: 25
  },
  {
    id: 'PKG_1752326131055_def456',
    createdAt: '2025-01-12T14:15:00Z',
    pickup: {
      address: 'Nablus City Center',
      lat: 32.2211,
      lng: 35.2544
    },
    dropoff: {
      address: 'Balata Refugee Camp, Nablus',
      lat: 32.2100,
      lng: 35.2600
    },
    receiverName: 'Fatima Ahmad',
    receiverPhone: '+970568987654',
    packageType: 'Electronics',
    status: 'On the Way' as const,
    estimatedTime: '20-30 mins',
    driverName: 'Mohammed Ali',
    driverPhone: '0599123456',
    cost: 18
  }
];

const mockPickupRequests = [
  {
    id: 'REQ_1752326131056_ghi789',
    createdAt: '2025-01-12T16:45:00Z',
    pickup: {
      address: 'Rafidia, Nablus',
      lat: 32.2278,
      lng: 35.2542
    },
    packageType: 'Clothing',
    status: 'Preparing' as const,
    estimatedTime: '45-60 mins',
    driverName: 'Ahmad Samer',
    driverPhone: '0595956014',
    cost: 15,
    notes: 'Please call when you arrive'
  }
];

type PackageStatus = 'All' | 'Delivered' | 'On the Way' | 'Preparing' | 'Pending';
type TabType = 'sent' | 'pickup';

const PackagesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('sent');
  const [selectedFilter, setSelectedFilter] = useState<PackageStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [sentPackages, setSentPackages] = useState<any[]>([]);
  const [pickupRequests, setPickupRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch packages from backend on component mount
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const { packages: backendPackages } = await getUserPackages();

        // Separate sent packages and pickup requests
        const sent: any[] = [];
        const pickup: any[] = [];

        backendPackages.forEach((pkg: any) => {
          if (pkg.deliveryAddress) {
            // It's a sent package
            sent.push({
              id: pkg.trackingNumber,
              createdAt: pkg.createdAt,
              pickup: {
                address: pkg.pickupAddress.street,
                lat: pkg.pickupAddress.coordinates.lat,
                lng: pkg.pickupAddress.coordinates.lng
              },
              dropoff: {
                address: pkg.deliveryAddress.street,
                lat: pkg.deliveryAddress.coordinates.lat,
                lng: pkg.deliveryAddress.coordinates.lng
              },
              receiverName: pkg.recipientInfo.name,
              receiverPhone: pkg.recipientInfo.phone,
              packageType: pkg.packageDetails.type,
              status: pkg.status,
              estimatedTime: pkg.estimatedDeliveryTime || '30-45 mins',
              driverName: pkg.driverName || 'Driver',
              driverPhone: pkg.driverPhone || '',
              cost: pkg.cost
            });
          } else {
            // It's a pickup request
            pickup.push({
              id: pkg.trackingNumber,
              createdAt: pkg.createdAt,
              pickup: {
                address: pkg.pickupAddress.street,
                lat: pkg.pickupAddress.coordinates.lat,
                lng: pkg.pickupAddress.coordinates.lng
              },
              packageType: pkg.packageDetails.type,
              status: pkg.status,
              estimatedTime: pkg.estimatedPickupTime || '45-60 mins',
              driverName: pkg.driverName || 'Driver',
              driverPhone: pkg.driverPhone || '',
              cost: pkg.cost,
              notes: pkg.notes || ''
            });
          }
        });

        setSentPackages(sent);
        setPickupRequests(pickup);
      } catch (error) {
        console.error('Error fetching packages:', error);
        // Fallback to mock data
        setSentPackages(mockSentPackages);
        setPickupRequests(mockPickupRequests);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  const currentData = activeTab === 'sent' ? sentPackages : pickupRequests;

  const filteredPackages = useMemo(() => {
    let packages = selectedFilter === 'All' ? currentData : currentData.filter(pkg => pkg.status === selectedFilter);

    if (searchQuery.trim()) {
      packages = packages.filter(pkg =>
        pkg.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.pickup.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ('dropoff' in pkg && pkg.dropoff.address.toLowerCase().includes(searchQuery.toLowerCase())) ||
        ('receiverName' in pkg && pkg.receiverName.toLowerCase().includes(searchQuery.toLowerCase())) ||
        pkg.packageType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return packages;
  }, [activeTab, selectedFilter, searchQuery, currentData]);

  const filters: { key: PackageStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Packages', color: 'bg-gray-100 text-gray-700' },
    { key: 'Delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'On the Way', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'Preparing', label: 'Preparing', color: 'bg-blue-100 text-blue-700' },
    { key: 'Pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={16} className="text-green-600" />;
      case 'On the Way': return <Truck size={16} className="text-orange-600" />;
      case 'Preparing': return <Clock size={16} className="text-blue-600" />;
      default: return <Package size={16} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-600 bg-green-50 border-green-200';
      case 'On the Way': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'Preparing': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-primary-100/20 to-third-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-secondary-100/20 to-primary-100/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Packages</h1>
            <p className="text-gray-600">Track your sent packages and pickup requests</p>
          </div>
        </motion.div>

        {/* Tabs - Matching Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-6"
        >
          <div className="bg-white rounded-xl shadow-md border border-gray-200 p-4">
            <div className="flex justify-center gap-2">
              <button
                onClick={() => setActiveTab('sent')}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeTab === 'sent'
                    ? 'bg-primary-600 text-white shadow-lg scale-105'
                    : 'bg-gray-100 text-gray-700 hover:scale-105'
                }`}
              >
                📦 Sent Packages
              </button>
              <button
                onClick={() => setActiveTab('pickup')}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeTab === 'pickup'
                    ? 'bg-primary-600 text-white shadow-lg scale-105'
                    : 'bg-gray-100 text-gray-700 hover:scale-105'
                }`}
              >
                📥 Pickup Requests
              </button>
            </div>
          </div>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-6"
        >
          <div className="bg-white rounded-xl shadow-md border border-gray-200">
            <div className="flex items-center gap-3 p-4">
              <Search size={20} className="text-gray-400" />
              <input
                type="text"
                placeholder="Search packages, addresses, or recipients..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 bg-transparent text-gray-800 placeholder-gray-500 outline-none text-base"
              />
              {searchQuery.trim() && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X size={18} className="text-gray-400" />
                </button>
              )}
            </div>
          </div>
        </motion.div>

        {/* Filter Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-6"
        >
          <div className="bg-white rounded-xl shadow-md border border-gray-200 p-4">
            <div className="flex flex-wrap gap-2">
              {filters.map((filter) => (
                <button
                  key={filter.key}
                  onClick={() => setSelectedFilter(filter.key)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedFilter === filter.key
                      ? 'bg-primary-600 text-white shadow-lg scale-105'
                      : filter.color + ' hover:scale-105'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Packages List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="space-y-4"
        >
          {filteredPackages.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl p-12 text-center shadow-lg border border-gray-100"
            >
              <Package size={64} className="text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                {activeTab === 'sent' ? 'No Sent Packages' : 'No Pickup Requests'}
              </h3>
              <p className="text-gray-500">
                {searchQuery.trim()
                  ? `No packages match "${searchQuery}"`
                  : activeTab === 'sent'
                    ? "You haven't sent any packages yet"
                    : "You haven't requested any pickups yet"
                }
              </p>
            </motion.div>
          ) : (
            <AnimatePresence>
              {filteredPackages.map((pkg, index) => (
                <motion.div
                  key={pkg.id}
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -20, scale: 0.95 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100
                  }}
                  whileHover={{ y: -4, transition: { duration: 0.2 } }}
                  className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300"
                >
                  {/* Package Header */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-12 h-12 rounded-xl flex items-center justify-center"
                          style={{ backgroundColor: '#8F3DD2' }}
                        >
                          {activeTab === 'sent' ? (
                            <Package className="text-white" size={24} />
                          ) : (
                            <RotateCcw className="text-white" size={24} />
                          )}
                        </div>
                        <div>
                          <h3 className="font-bold text-lg text-gray-900">#{pkg.id.slice(-8)}</h3>
                          <p className="text-gray-600 text-sm">{pkg.packageType}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(pkg.status)}`}>
                          {getStatusIcon(pkg.status)}
                          {pkg.status}
                        </div>
                        <p className="text-gray-500 text-xs mt-1">
                          {formatDate(pkg.createdAt).date} • {formatDate(pkg.createdAt).time}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Package Details */}
                  <div className="p-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      {/* Left Column - Location Info */}
                      <div className="space-y-4">
                        {/* Pickup Location */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <MapPin size={16} />
                            {activeTab === 'sent' ? 'From' : 'Pickup Location'}
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-3">
                            <div className="flex items-start gap-2">
                              <MapPin size={14} className="text-gray-500 mt-1" />
                              <span className="text-gray-700 text-sm">{pkg.pickup.address}</span>
                            </div>
                          </div>
                        </div>

                        {/* Dropoff Location (for sent packages) */}
                        {activeTab === 'sent' && 'dropoff' in pkg && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <Navigation size={16} />
                              To
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3">
                              <div className="flex items-start gap-2">
                                <Navigation size={14} className="text-gray-500 mt-1" />
                                <span className="text-gray-700 text-sm">{pkg.dropoff.address}</span>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Recipient Info (for sent packages) */}
                        {activeTab === 'sent' && 'receiverName' in pkg && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <User size={16} />
                              Recipient
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                              <div className="flex items-center gap-2">
                                <User size={14} className="text-gray-500" />
                                <span className="text-gray-700 text-sm">{pkg.receiverName}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Phone size={14} className="text-gray-500" />
                                <span className="text-gray-700 text-sm">{pkg.receiverPhone}</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Right Column - Package & Driver Info */}
                      <div className="space-y-4">
                        {/* Package Info */}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            <Package size={16} />
                            Package Details
                          </h4>
                          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 text-sm">Type:</span>
                              <span className="font-medium text-sm">{pkg.packageType}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 text-sm">Est. Time:</span>
                              <span className="font-medium text-sm">{pkg.estimatedTime}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 text-sm">Cost:</span>
                              <span className="font-bold text-primary-600">₪{pkg.cost}</span>
                            </div>
                          </div>
                        </div>

                        {/* Driver Info */}
                        {pkg.driverName && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <Truck size={16} />
                              Driver
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600 text-sm">Name:</span>
                                <span className="font-medium text-sm">{pkg.driverName}</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600 text-sm">Phone:</span>
                                <span className="font-medium text-sm">{pkg.driverPhone}</span>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Notes (for pickup requests) */}
                        {activeTab === 'pickup' && 'notes' in pkg && pkg.notes && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                              <Hash size={16} />
                              Notes
                            </h4>
                            <div className="bg-gray-50 rounded-lg p-3">
                              <span className="text-gray-700 text-sm">{pkg.notes}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Action Button */}
                    <div className="mt-6 pt-4 border-t border-gray-100">
                      <button
                        onClick={() => {
                          // Navigate to package tracking
                          window.location.href = `/customer/packages/tracking/${pkg.id}?type=${activeTab}`;
                        }}
                        className="w-full md:w-auto px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center justify-center gap-2"
                      >
                        <Eye size={18} />
                        Track Package
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default PackagesPage;
