import { aiService } from '../services/aiService';
import { userContextService } from '../services/userContextService';
import { AIKnowledgeHelper } from '../services/aiKnowledgeBase';

// Mock AsyncStorage for testing
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('AI Chat System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AI Service', () => {
    test('should create a new conversation', () => {
      const conversationId = aiService.createConversation();
      expect(conversationId).toBeDefined();
      expect(typeof conversationId).toBe('string');
    });

    test('should handle message sending with fallback', async () => {
      const conversationId = aiService.createConversation();
      const response = await aiService.sendMessage(conversationId, 'Hello');
      
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
      expect(response.conversationId).toBe(conversationId);
    });

    test('should provide appropriate fallback responses', () => {
      const trackingResponse = aiService.getFallbackResponse('track my order');
      expect(trackingResponse).toContain('track');
      expect(trackingResponse).toContain('order');

      const supplierResponse = aiService.getFallbackResponse('find restaurants');
      expect(supplierResponse).toContain('supplier');
      
      const packageResponse = aiService.getFallbackResponse('send package');
      expect(packageResponse).toContain('package');
    });

    test('should get conversation by ID', () => {
      const conversationId = aiService.createConversation();
      const conversation = aiService.getConversation(conversationId);
      
      expect(conversation).toBeDefined();
      expect(conversation?.id).toBe(conversationId);
    });

    test('should delete conversation', async () => {
      const conversationId = aiService.createConversation();
      const deleted = await aiService.deleteConversation(conversationId);
      
      expect(deleted).toBe(true);
      
      const conversation = aiService.getConversation(conversationId);
      expect(conversation).toBeNull();
    });
  });

  describe('User Context Service', () => {
    test('should initialize user context', async () => {
      const context = await userContextService.initializeUserContext('test_user');
      
      expect(context.userId).toBe('test_user');
      expect(context.location).toBeDefined();
      expect(context.recentOrders).toBeDefined();
      expect(context.preferences).toBeDefined();
    });

    test('should get nearby suppliers', () => {
      const suppliers = userContextService.getNearbySuppliers();
      expect(Array.isArray(suppliers)).toBe(true);
      expect(suppliers.length).toBeGreaterThan(0);
    });

    test('should get suppliers by category', () => {
      const restaurants = userContextService.getNearbySuppliers('restaurant');
      expect(Array.isArray(restaurants)).toBe(true);
      
      if (restaurants.length > 0) {
        expect(restaurants[0].category).toBe('restaurant');
      }
    });

    test('should get favorite suppliers', () => {
      const favorites = userContextService.getFavoriteSuppliers();
      expect(Array.isArray(favorites)).toBe(true);
    });

    test('should get order tracking info', () => {
      const activeOrders = userContextService.getOrderTrackingInfo();
      expect(Array.isArray(activeOrders)).toBe(true);
    });

    test('should generate context summary', () => {
      const summary = userContextService.getContextSummary();
      expect(typeof summary).toBe('string');
      expect(summary.length).toBeGreaterThan(0);
      expect(summary).toContain('User Context:');
    });

    test('should get smart suggestions', () => {
      const suggestions = userContextService.getSmartSuggestions();
      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBeLessThanOrEqual(4);
    });
  });

  describe('AI Knowledge Helper', () => {
    test('should find relevant knowledge for queries', () => {
      const trackingKnowledge = AIKnowledgeHelper.findRelevantKnowledge('track order');
      expect(Array.isArray(trackingKnowledge)).toBe(true);
      expect(trackingKnowledge.length).toBeGreaterThan(0);

      const supplierKnowledge = AIKnowledgeHelper.findRelevantKnowledge('find restaurant');
      expect(Array.isArray(supplierKnowledge)).toBe(true);
      expect(supplierKnowledge.length).toBeGreaterThan(0);
    });

    test('should get context for queries', () => {
      const context = AIKnowledgeHelper.getContextForQuery('track my order');
      expect(typeof context).toBe('string');
      expect(context.length).toBeGreaterThan(0);
    });

    test('should get quick actions for queries', () => {
      const trackingActions = AIKnowledgeHelper.getQuickActionsForQuery('track order');
      expect(Array.isArray(trackingActions)).toBe(true);
      expect(trackingActions).toContain('Track Order');

      const foodActions = AIKnowledgeHelper.getQuickActionsForQuery('order food');
      expect(Array.isArray(foodActions)).toBe(true);
      expect(foodActions).toContain('Browse Restaurants');
    });

    test('should get all knowledge context', () => {
      const allContext = AIKnowledgeHelper.getAllKnowledgeContext();
      expect(typeof allContext).toBe('string');
      expect(allContext).toContain('Complete Wasel Knowledge Base:');
    });
  });

  describe('Integration Tests', () => {
    test('should handle complete chat flow', async () => {
      // Initialize user context
      await userContextService.initializeUserContext('test_user');
      
      // Create conversation
      const conversationId = aiService.createConversation();
      
      // Send message with context
      const userContext = userContextService.getContextSummary();
      const knowledgeContext = AIKnowledgeHelper.getContextForQuery('track order');
      
      const response = await aiService.sendMessage(conversationId, 'I want to track my order', {
        userContext,
        knowledgeContext
      });
      
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
      expect(response.message).toContain('track');
    });

    test('should handle error scenarios gracefully', async () => {
      const conversationId = 'invalid_id';
      const response = await aiService.sendMessage(conversationId, 'test message');
      
      // Should still provide a response even with invalid conversation ID
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
    });

    test('should provide contextual suggestions based on user data', async () => {
      await userContextService.initializeUserContext('test_user');
      const suggestions = userContextService.getSmartSuggestions();
      
      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBeGreaterThan(0);
      
      // Should include relevant suggestions based on mock data
      const suggestionText = suggestions.join(' ');
      expect(suggestionText).toMatch(/(track|order|restaurant|supplier)/i);
    });
  });

  describe('Performance Tests', () => {
    test('should handle multiple concurrent conversations', async () => {
      const conversations = [];
      const promises = [];
      
      // Create multiple conversations
      for (let i = 0; i < 5; i++) {
        const conversationId = aiService.createConversation();
        conversations.push(conversationId);
        
        // Send messages concurrently
        promises.push(
          aiService.sendMessage(conversationId, `Test message ${i}`)
        );
      }
      
      const responses = await Promise.all(promises);
      
      responses.forEach((response, index) => {
        expect(response.success).toBe(true);
        expect(response.message).toBeDefined();
        expect(response.conversationId).toBe(conversations[index]);
      });
    });

    test('should handle large message history efficiently', async () => {
      const conversationId = aiService.createConversation();
      
      // Send multiple messages to build history
      for (let i = 0; i < 10; i++) {
        await aiService.sendMessage(conversationId, `Message ${i}`);
      }
      
      const conversation = aiService.getConversation(conversationId);
      expect(conversation).toBeDefined();
      expect(conversation?.messages.length).toBeGreaterThan(10); // Including system message
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty messages', async () => {
      const conversationId = aiService.createConversation();
      const response = await aiService.sendMessage(conversationId, '');
      
      // Should handle gracefully
      expect(response.success).toBe(true);
    });

    test('should handle very long messages', async () => {
      const conversationId = aiService.createConversation();
      const longMessage = 'a'.repeat(1000);
      const response = await aiService.sendMessage(conversationId, longMessage);
      
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
    });

    test('should handle special characters and emojis', async () => {
      const conversationId = aiService.createConversation();
      const specialMessage = 'Hello! 🚚 Can you help me track my order? 📦 Order #123-456';
      const response = await aiService.sendMessage(conversationId, specialMessage);
      
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
    });

    test('should handle Arabic text', async () => {
      const conversationId = aiService.createConversation();
      const arabicMessage = 'مرحبا، أريد تتبع طلبي';
      const response = await aiService.sendMessage(conversationId, arabicMessage);
      
      expect(response.success).toBe(true);
      expect(response.message).toBeDefined();
    });
  });
});
