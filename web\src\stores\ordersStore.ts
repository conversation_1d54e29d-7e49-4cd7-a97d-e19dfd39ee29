import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type OrderItem = {
  product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
  };
  qty: number;
  finalPrice: number;
  selectedAdditions?: Array<{ id: string; name: string; price: number }>;
  selectedSides?: Array<{ id: string; name: string; price: number }>;
  without?: string[];
  selectedSize?: string;
  selectedColor?: string;
};

export type Order = {
  id: string;
  createdAt: string;
  items: OrderItem[];
  supplier: {
    id: string;
    name: string;
  };
  subtotal: number;
  deliveryFee: number;
  promoDiscount: number;
  total: number;
  status: 'Pending' | 'Confirmed' | 'Preparing' | 'Ready' | 'On the Way' | 'Delivered' | 'Cancelled';
  address: string;
  phone: string;
  notes?: string;
  paymentMethod: 'cash' | 'card';
  promoCode?: string;
  estimatedTime: string;
  placedAt: string;
  driverName?: string;
  driverPhone?: string;
};

type OrdersState = {
  orders: Order[];
  addOrder: (order: Order) => void;
  addMultipleOrders: (orders: Order[]) => void;
  updateOrderStatus: (orderId: string, status: Order['status']) => void;
  getOrderById: (orderId: string) => Order | undefined;
  getOrdersByStatus: (status: Order['status']) => Order[];
  clearOrders: () => void;
};

// Mock orders for development
const mockOrders: Order[] = [
  {
    id: 'ORD_1752326131054_abc123',
    createdAt: '2025-01-12T10:30:00Z',
    items: [
      {
        product: {
          id: 'chicken-shawarma-1',
          name: 'Chicken Shawarma',
          image: 'https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=400&fit=crop',
          price: 22,
          category: 'main'
        },
        qty: 2,
        finalPrice: 25
      },
      {
        product: {
          id: 'french-fries-1',
          name: 'French Fries',
          image: 'https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=400&h=400&fit=crop',
          price: 10,
          category: 'sides'
        },
        qty: 1,
        finalPrice: 12
      }
    ],
    supplier: {
      id: 'SUP_123',
      name: 'Al-Quds Restaurant'
    },
    subtotal: 50,
    deliveryFee: 12,
    promoDiscount: 0,
    total: 62,
    status: 'Delivered',
    address: 'Nablus, Palestine',
    phone: '+970568406041',
    paymentMethod: 'cash',
    estimatedTime: '30-45 mins',
    placedAt: '2025-01-12T10:30:00Z',
    driverName: 'Ahmad Samer',
    driverPhone: '0595956014'
  },
  {
    id: 'ORD_1752326131055_def456',
    createdAt: '2025-01-12T14:15:00Z',
    items: [
      {
        product: {
          id: 'margherita-pizza-1',
          name: 'Margherita Pizza',
          image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=400&fit=crop',
          price: 40,
          category: 'main'
        },
        qty: 1,
        finalPrice: 45
      },
      {
        product: {
          id: 'coca-cola-1',
          name: 'Coca Cola',
          image: 'https://images.unsplash.com/photo-1581636625402-29b2a704ef13?w=400&h=400&fit=crop',
          price: 4,
          category: 'drinks'
        },
        qty: 2,
        finalPrice: 8
      }
    ],
    supplier: {
      id: 'SUP_124',
      name: 'Pizza Palace'
    },
    subtotal: 48,
    deliveryFee: 10,
    promoDiscount: 5,
    total: 53,
    status: 'On the Way',
    address: 'Ramallah, Palestine',
    phone: '+970568406041',
    paymentMethod: 'card',
    estimatedTime: '25-35 mins',
    placedAt: '2025-01-12T14:15:00Z',
    driverName: 'Mohammed Ali',
    driverPhone: '0595956015'
  },
  {
    id: 'ORD_1752326131056_ghi789',
    createdAt: '2025-01-12T16:45:00Z',
    items: [
      {
        product: {
          id: 'burger-deluxe-1',
          name: 'Deluxe Burger',
          image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop',
          price: 28,
          category: 'main'
        },
        qty: 1,
        finalPrice: 30
      }
    ],
    supplier: {
      id: 'SUP_125',
      name: 'Burger House'
    },
    subtotal: 30,
    deliveryFee: 8,
    promoDiscount: 0,
    total: 38,
    status: 'Preparing',
    address: 'Jerusalem, Palestine',
    phone: '+970568406041',
    paymentMethod: 'cash',
    estimatedTime: '40-50 mins',
    placedAt: '2025-01-12T16:45:00Z',
    driverName: 'Khalil Omar',
    driverPhone: '0595956016'
  }
];

export const useOrdersStore = create<OrdersState>()(
  persist(
    (set, get) => ({
      orders: mockOrders,

      addOrder: (order) => {
        set((state) => ({
          orders: [order, ...state.orders] // Add new order at the beginning
        }));
      },

      addMultipleOrders: (orders) => {
        set((state) => ({
          orders: [...orders, ...state.orders] // Add new orders at the beginning
        }));
      },

      updateOrderStatus: (orderId, status) => {
        set((state) => ({
          orders: state.orders.map((order) =>
            order.id === orderId ? { ...order, status } : order
          )
        }));
      },

      getOrderById: (orderId) => {
        return get().orders.find((order) => order.id === orderId);
      },

      getOrdersByStatus: (status) => {
        return get().orders.filter((order) => order.status === status);
      },

      clearOrders: () => {
        set({ orders: [] });
      }
    }),
    {
      name: 'wasel-orders-storage', // unique name for localStorage
    }
  )
);
