import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Star, Clock, Phone, MapPin, Search, Plus, Minus, ShoppingCart } from 'lucide-react';
import { useCartStore } from '../../stores/cartStore';
import { getSupplierById, Supplier } from '../../../../services/apiService';

const SupplierDetailsPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const supplierId = searchParams.get('supplierId');
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSupplier = async () => {
      if (!supplierId) {
        setError('No supplier ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const supplierData = await getSupplierById(supplierId);
        setSupplier(supplierData);
        setError(null);
      } catch (err) {
        console.error('Error fetching supplier:', err);
        setError('Failed to load supplier details');
        setSupplier(null);
      } finally {
        setLoading(false);
      }
    };

    fetchSupplier();
  }, [supplierId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900">Loading supplier details...</h2>
        </div>
      </div>
    );
  }

  if (error || !supplier) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Supplier Not Found</h2>
          <button
            onClick={() => navigate(-1)}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Get products from supplier data
  const products = supplier.products || [];

  // Get promotions filtered by supplier category
  const supplierPromotions = promotions.filter(promo => promo.category === supplier.category);
  
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const { addItem, items, totalQty } = useCartStore();

  // Get unique categories from products
  const categories = ['All', ...Array.from(new Set(products.map(p => p.category)))];

  const filteredProducts = useMemo(() => {
    let filteredProducts = products;

    if (selectedCategory !== 'All') {
      filteredProducts = filteredProducts.filter(product => product.category === selectedCategory);
    }

    if (searchQuery.trim()) {
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        ((product as any).description && (product as any).description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filteredProducts;
  }, [products, selectedCategory, searchQuery]);

  const addToCart = (product: any) => {
    addItem({
      product: {
        id: product.id,
        name: product.name,
        image: product.image,
        price: product.price,
        category: product.category
      },
      qty: 1,
      finalPrice: product.price,
      supplierId: supplier.id,
      supplierName: supplier.name,
      supplierCategory: supplier.category
    });
  };

  const getCartQty = (productId: string) => {
    const item = items.find(i =>
      i.product.id === productId &&
      i.supplierId === supplier.id
    );
    return item ? item.qty : 0;
  };

  const handleProductClick = (product: any) => {
    navigate(`/customer/supplier-product-details?productId=${product.id}&supplierId=${supplierId}&category=${supplier.category}`, {
      state: { product, supplier }
    });
  };

  const handleCheckout = () => {
    const supplierItems = items.filter(i => i.supplierId === supplier.id);
    if (supplierItems.length > 0) {
      navigate('/customer/order-checkout', {
        state: {
          itemsBySupplier: { [supplier.id]: supplierItems },
          totalWithoutFee: totalQty(supplier.id)
        }
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <h1 className="text-2xl font-bold text-gray-900">{supplier.name}</h1>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Supplier Info */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
          <div className="relative">
            <img
              src={supplier.banner}
              alt={supplier.name}
              className="w-full h-48 object-cover"
            />
            <div className="absolute bottom-4 left-4">
              <img
                src={supplier.logoUrl}
                alt={supplier.name}
                className="w-16 h-16 rounded-lg border-4 border-white shadow-lg"
              />
            </div>
          </div>

          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">{supplier.name}</h2>
                {(supplier as any).description && (
                  <p className="text-gray-600 mt-1">{(supplier as any).description}</p>
                )}
              </div>
              <span className={`px-3 py-1 text-sm rounded-full ${
                (supplier as any).isOpen !== false
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                {(supplier as any).isOpen !== false ? 'Open' : 'Closed'}
              </span>
            </div>
            
            <div className="flex items-center gap-6 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span>{supplier.rating}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{supplier.deliveryTime}</span>
              </div>
              <div className="flex items-center gap-1">
                <Phone className="w-4 h-4" />
                <span>{supplier.phone}</span>
              </div>
            </div>

            <div className="flex gap-2">
              {supplier.tags?.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Promotions */}
        {supplierPromotions.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🔥 Current Promotions</h3>
            <div className="flex gap-4 overflow-x-auto">
              {supplierPromotions.map((promotion) => (
                <div
                  key={promotion.id}
                  className="flex-shrink-0 bg-gradient-to-r from-purple-500 to-pink-500 text-white p-4 rounded-lg min-w-[250px]"
                >
                  <h4 className="font-semibold">{promotion.title}</h4>
                  <p className="text-sm mt-1 opacity-90">{promotion.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Search and Categories */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products…"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            {/* Category Tabs */}
            <div className="flex gap-2 overflow-x-auto">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                    selectedCategory === category
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Products */}
        <div className="space-y-4">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex gap-4">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-20 h-20 object-cover rounded-lg cursor-pointer"
                  onClick={() => handleProductClick(product)}
                />
                <div className="flex-1">
                  <h4
                    className="font-semibold text-lg text-gray-900 cursor-pointer hover:text-purple-600"
                    onClick={() => handleProductClick(product)}
                  >
                    {product.name}
                  </h4>
                  {(product as any).description && (
                    <p className="text-gray-600 text-sm mt-1">{(product as any).description}</p>
                  )}
                  <p className="text-lg font-bold text-purple-600 mt-2">₪{product.price}</p>
                </div>
                <div className="flex items-center gap-2">
                  {getCartQty(product.id) > 0 ? (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                          if (item && item.qty > 1) {
                            useCartStore.getState().updateQty(item.id, item.qty - 1);
                          } else if (item) {
                            useCartStore.getState().removeItem(item.id);
                          }
                        }}
                        className="w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      <span className="w-8 text-center font-semibold">{getCartQty(product.id)}</span>
                      <button
                        onClick={() => {
                          const item = items.find(i => i.product.id === product.id && i.supplierId === supplier.id);
                          if (item) {
                            useCartStore.getState().updateQty(item.id, item.qty + 1);
                          }
                        }}
                        className="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center hover:bg-green-200"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => addToCart(product)}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      Add to Cart
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search terms or category</p>
          </div>
        )}
      </div>

    </div>
  );
};

export default SupplierDetailsPage;
