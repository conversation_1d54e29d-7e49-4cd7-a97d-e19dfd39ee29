import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, Clock, MapPin, Phone, User, Package, CheckCircle, Truck } from 'lucide-react';
import { useOrdersStore } from '../../stores/ordersStore';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const OrderTrackingPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { getOrderById } = useOrdersStore();
  const orderId = searchParams.get('orderId');

  // Get order from store
  const order = orderId ? getOrderById(orderId) : null;

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-6">The order you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/customer/orders')}
            className="bg-purple-600 text-white py-2 px-6 rounded-lg font-semibold hover:bg-purple-700 transition-colors"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  // Mock location data for tracking
  const trackingData = {
    supplierLocation: { lat: 32.2211, lng: 35.2544 },
    deliveryLocation: { lat: 32.2156, lng: 35.2631 },
    driverLocation: { lat: 32.2180, lng: 35.2580 }, // Between supplier and customer
  };

  // Order status timeline
  const statusTimeline = [
    { 
      status: 'Order Placed', 
      time: '10:30 AM', 
      completed: true, 
      description: 'Your order has been placed successfully' 
    },
    { 
      status: 'Order Confirmed', 
      time: '10:32 AM', 
      completed: true, 
      description: 'Restaurant confirmed your order' 
    },
    { 
      status: 'Preparing', 
      time: '10:35 AM', 
      completed: true, 
      description: 'Your food is being prepared' 
    },
    { 
      status: 'Out for Delivery', 
      time: '11:00 AM', 
      completed: true, 
      description: 'Driver is on the way to deliver your order' 
    },
    { 
      status: 'Delivered', 
      time: 'ETA 11:15 AM', 
      completed: false, 
      description: 'Order will be delivered to your address' 
    }
  ];

  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCallDriver = () => {
    window.open(`tel:${order.driverPhone}`);
  };

  const handleViewOrderDetails = () => {
    navigate(`/customer/order-details?orderId=${order.id}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Track Order</h1>
              <p className="text-gray-600">Order #{order.id}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Order Info */}
          <div className="space-y-6">
            {/* Current Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Truck className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">{order.status}</h2>
                  <p className="text-gray-600">ETA: {order.estimatedTime}</p>
                </div>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-800 font-medium">
                  Your order is on the way! {order.driverName} will deliver it soon.
                </p>
              </div>
            </div>

            {/* Driver Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Driver Information</h3>
              
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-purple-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900">{order.driverName}</h4>
                  <p className="text-gray-600">{order.driverPhone}</p>
                </div>
                <button
                  onClick={handleCallDriver}
                  className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Phone className="w-4 h-4" />
                  Call
                </button>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Timeline</h3>
              
              <div className="space-y-4">
                {statusTimeline.map((step, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      step.completed 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-400'
                    }`}>
                      {step.completed ? (
                        <CheckCircle className="w-5 h-5" />
                      ) : (
                        <Clock className="w-5 h-5" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className={`font-medium ${
                          step.completed ? 'text-gray-900' : 'text-gray-500'
                        }`}>
                          {step.status}
                        </h4>
                        <span className={`text-sm ${
                          step.completed ? 'text-gray-600' : 'text-gray-400'
                        }`}>
                          {step.time}
                        </span>
                      </div>
                      <p className={`text-sm ${
                        step.completed ? 'text-gray-600' : 'text-gray-400'
                      }`}>
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">From:</span>
                  <span className="text-gray-600">{order.supplier.name}</span>
                </div>
                
                <div className="space-y-2">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">{item.qty} × {item.product.name}</span>
                    </div>
                  ))}
                </div>
                
                <hr />
                
                <div className="flex justify-between items-center font-semibold">
                  <span>Total:</span>
                  <span>₪{order.total}</span>
                </div>
              </div>
              
              <button
                onClick={handleViewOrderDetails}
                className="w-full mt-4 py-2 px-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                View Full Order Details
              </button>
            </div>
          </div>

          {/* Right Column - Map */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Live Tracking</h3>
                <p className="text-gray-600">Follow your driver's location in real-time</p>
              </div>
              
              <div className="h-96">
                <MapContainer
                  center={[order.driverLocation.lat, order.driverLocation.lng]}
                  zoom={14}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  
                  {/* Supplier Marker */}
                  <Marker position={[trackingData.supplierLocation.lat, trackingData.supplierLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>{order.supplier.name}</strong>
                        <br />
                        <span className="text-sm text-gray-600">Restaurant</span>
                      </div>
                    </Popup>
                  </Marker>
                  
                  {/* Driver Marker */}
                  <Marker position={[trackingData.driverLocation.lat, trackingData.driverLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>{order.driverName}</strong>
                        <br />
                        <span className="text-sm text-gray-600">Your Driver</span>
                      </div>
                    </Popup>
                  </Marker>
                  
                  {/* Delivery Location Marker */}
                  <Marker position={[trackingData.deliveryLocation.lat, trackingData.deliveryLocation.lng]}>
                    <Popup>
                      <div className="text-center">
                        <strong>Delivery Address</strong>
                        <br />
                        <span className="text-sm text-gray-600">{order.address}</span>
                      </div>
                    </Popup>
                  </Marker>
                </MapContainer>
              </div>
            </div>

            {/* Map Legend */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h4 className="font-semibold text-gray-900 mb-3">Map Legend</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Restaurant Location</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Driver Location (Live)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Your Delivery Address</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderTrackingPage;
