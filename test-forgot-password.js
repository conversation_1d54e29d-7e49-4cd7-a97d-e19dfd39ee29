// Simple test script to verify code-based forgot password functionality
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3000/api';
const TEST_EMAIL = '<EMAIL>'; // Use the email from .env

async function testCodeBasedForgotPassword() {
  console.log('🧪 Testing Code-Based Forgot Password API...\n');

  try {
    // Step 1: Test forgot password endpoint (should send verification code)
    console.log('📧 Step 1: Testing forgot password request...');
    const forgotResponse = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL
      })
    });

    const forgotResult = await forgotResponse.json();
    console.log('Forgot Password Response:', JSON.stringify(forgotResult, null, 2));

    if (forgotResult.success) {
      console.log('✅ Verification code sent successfully!');

      // In development mode, we should get the verification code
      if (forgotResult.data && forgotResult.data.verificationCode) {
        const verificationCode = forgotResult.data.verificationCode;
        console.log('🔑 Verification code received:', verificationCode);

        // Step 2: Test code verification endpoint
        console.log('\n🔍 Step 2: Testing code verification...');
        const verifyResponse = await fetch(`${API_BASE_URL}/auth/verify-reset-code`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: TEST_EMAIL,
            code: verificationCode
          })
        });

        const verifyResult = await verifyResponse.json();
        console.log('Verify Code Response:', JSON.stringify(verifyResult, null, 2));

        if (verifyResult.success) {
          console.log('✅ Code verification successful!');

          // Step 3: Test reset password endpoint
          console.log('\n🔒 Step 3: Testing password reset...');
          const resetResponse = await fetch(`${API_BASE_URL}/auth/reset-password`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: TEST_EMAIL,
              code: verificationCode,
              password: 'NewPassword123'
            })
          });

          const resetResult = await resetResponse.json();
          console.log('Reset Password Response:', JSON.stringify(resetResult, null, 2));

          if (resetResult.success) {
            console.log('✅ Password reset successful!');
            console.log('\n🎉 All tests passed! Code-based forgot password is working correctly.');
          } else {
            console.log('❌ Password reset failed:', resetResult.message);
          }
        } else {
          console.log('❌ Code verification failed:', verifyResult.message);
        }
      } else {
        console.log('⚠️ No verification code in response (production mode)');
        console.log('📧 Check your email for the verification code');
      }
    } else {
      console.log('❌ Forgot password request failed:', forgotResult.message);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('\n💡 Make sure the backend server is running on port 3000');
    console.log('   Run: cd backend && npm run dev');
  }
}

// Run the test
testCodeBasedForgotPassword();
