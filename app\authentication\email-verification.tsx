import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { EmailVerificationGUI } from '../../components/authentication-components/EmailVerificationGUI';

export default function EmailVerificationPage() {
  const { email } = useLocalSearchParams<{ email: string }>();

  if (!email) {
    return null; // or redirect to signup
  }

  return <EmailVerificationGUI email={email} />;
}
